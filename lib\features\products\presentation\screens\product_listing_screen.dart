import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/utils/color_utils.dart';
import 'package:rozana/features/cart/presentation/widgets/floating_cart_wrapper.dart';
import 'package:rozana/features/search/services/typesense_service.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/services/remote_config_service.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_dimensions.dart';
import '../../../../data/services/data_loading_manager.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../widgets/custom_image.dart';
import '../../../../widgets/custom_text.dart';
import '../../../categories/bloc/categories_bloc.dart';
import '../../../home/<USER>/home bloc/home_bloc.dart';
import '../../bloc/listing_bloc/product_listing_event.dart';

import '../../../categories/presentation/widgets/subcategories_section.dart';
import '../../bloc/listing_bloc/product_listing_bloc.dart';
import '../../bloc/listing_bloc/product_listing_state.dart';
import '../widgets/product_section.dart';

class ProductListingScreen extends StatefulWidget {
  final CategoryEntity? category;
  final CategoryEntity? selectedSubcategory;
  final CategoryEntity? parentCategory;
  final Map<String, dynamic>? dynamicQuery;

  const ProductListingScreen({
    super.key,
    this.category,
    this.selectedSubcategory,
    this.parentCategory,
    this.dynamicQuery,
  });

  @override
  State<ProductListingScreen> createState() => _ProductListingScreenState();
}

class _ProductListingScreenState extends State<ProductListingScreen> {
  final ScrollController _scrollController = ScrollController();

  CategoryEntity? _selectedParent;
  CategoryEntity? _selectedCategory;
  CategoryEntity? _selectedSubCategory;
  CategoryEntity? _initialCategory;

  Map<String, dynamic> themeSettings = {
    "background_color": "#FFFFFF",
    "topbar_color": "#0FFF9E5",
    "highlight_color": "#BEABD3",
    "divider_color": "#5C2C90",
    "icon_primary_color": "#371A56",
    "icon_secondary_color": "#4A2373"
  };

  @override
  void initState() {
    super.initState();
    themeSettings =
        (RemoteConfigService().getThemeConfig['plp']?.isNotEmpty ?? false)
            ? RemoteConfigService().getThemeConfig['plp']
            : themeSettings;
    _selectedParent = widget.parentCategory;
    _selectedCategory = widget.category;
    if (widget.parentCategory == null) {
      fetchParentCategry();
    }
    // Log category view event to AppsFlyer when screen initializes
    if (widget.category != null) {
      AppsFlyerEvents.categoryView(
        widget.category?.id ?? '',
        widget.category?.name ?? '',
      );
    }
    if (widget.selectedSubcategory != null) {
      _initialCategory = widget.selectedSubcategory;
    }
    // Add scroll listener for debugging/pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        debugPrint('Reached 80% of scroll, should load more products');
        // In a real app, you would dispatch a LoadMoreProductsEvent here:
        // context.read<ProductListingBloc>().add(ProductListingEvent.loadMoreProducts());
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> fetchParentCategry() async {
    TypesenseService service = getIt<TypesenseService>();
    List<CategoryEntity> result = await service.searchSubCategories(
        category: widget.category, useCollectionId: true);
    if (result.isNotEmpty) {
      CategoryEntity item = result[0];
      setState(() {
        _selectedParent = CategoryEntity(
          id: item.parentID ?? '',
          name: item.name,
          count: 0,
          level: 'sub_category',
          collectionId: item.parentID ?? '',
        );
        _selectedCategory = _selectedCategory?.copyWith(name: item.name);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    final Color iconPrimaryColor =
        ColorUtils.hexToColor(themeSettings['icon_primary_color']) ??
            AppColors.primary700;

    final Color iconSecondaryColor =
        ColorUtils.hexToColor(themeSettings['icon_secondary_color']) ??
            AppColors.primary600;

    return FloatingCartWrapper(
        excludeOrderTile: true,
        child: Scaffold(
          backgroundColor:
              ColorUtils.hexToColor(themeSettings['background_color']) ??
                  AppColors.neutral100,
          appBar: AppBar(
            scrolledUnderElevation: 0,
            backgroundColor:
                ColorUtils.hexToColor(themeSettings['topbar_color']) ??
                    AppColors.yellow100,
            elevation: 0,
            centerTitle: false,
            titleSpacing: 0,
            leadingWidth: 0,
            automaticallyImplyLeading: false,
            title: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                    child: Image.asset(
                      'assets/new/icons/chevron_left.png',
                      height: 26,
                      width: 26,
                      color: iconSecondaryColor,
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      final ProductListingBloc productBloc =
                          context.read<ProductListingBloc>();
                      final HomeBloc homeBloc = getIt<HomeBloc>();

                      showTopCategorySelector(
                          context,
                          productBloc,
                          homeBloc,
                          _selectedCategory,
                          _selectedParent,
                          _selectedSubCategory, (parent, sub) {
                        context.pop();
                        if (parent != null && sub != null) {
                          if (_selectedSubCategory?.id != sub.id) {
                            setState(() {
                              _selectedParent = parent;
                              _selectedCategory = sub;
                            });
                          }
                        }
                      });
                    },
                    child: BlocBuilder<ProductListingBloc, ProductListingState>(
                      builder: (context, state) {
                        return state.maybeWhen(
                          initial: () => CustomText(
                            widget.category?.name ??
                                'Products', // Fallback while initial
                            color: iconPrimaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                          ),
                          loading: () => CustomText(
                            widget.category?.name ??
                                'Products', // Fallback while loading
                            color: iconPrimaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                          ),
                          loaded: (CategoryEntity? category,
                                  CategoryEntity? subCategory) =>
                              Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Flexible(
                                child: CustomText(
                                  ((_selectedCategory?.name.isNotEmpty ?? false)
                                          ? _selectedCategory?.name
                                          : category?.name) ??
                                      'Products',
                                  color: iconPrimaryColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ],
                          ),
                          error: (message) => CustomText(
                            'Error', // Or a more descriptive error title
                            color: iconPrimaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                          ),
                          // Default fallback for any unhandled states
                          orElse: () => CustomText(
                            widget.category?.name ?? 'Products',
                            color: iconPrimaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.fromLTRB(14, 6, 14, 6),
                child: InkWell(
                    onTap: () {
                      // context.pop();
                    },
                    child: Image.asset(
                      'assets/new/icons/search.png',
                      height: 24,
                      width: 24,
                      color: iconSecondaryColor,
                    )),
              ),
            ],
          ),
          body: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Categories section (1 part of flex)
              Expanded(
                flex: 1,
                child: Container(
                  decoration: BoxDecoration(
                    color: ColorUtils.hexToColor(
                            themeSettings['background_color']) ??
                        AppColors.neutral100,
                    boxShadow: [
                      BoxShadow(
                          offset: Offset(1, 0),
                          blurRadius: 2,
                          spreadRadius: 0,
                          color: AppColors.neutral800.withValues(alpha: 0.08))
                    ],
                  ),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(bottom: 150, top: 12),
                    child: SubCategoriesSection(
                      highliteSelectd: true,
                      currentSelectedCategory: _selectedSubCategory,
                      parentCategory: _selectedCategory,
                      dynamicQuery: widget.dynamicQuery,
                      useGridView: false,
                      showAsRow: true,
                      showTitleBar: false,
                      level: 'sub_sub_category',
                      dividerColor: themeSettings['divider_color'],
                      highlightPrimaryColor: themeSettings['highlight_color'],
                      highlightSecondaryColor:
                          themeSettings['background_color'],
                      onSubcategorySelected: (subCategory) {
                        if (_initialCategory != null) {
                          setState(() {
                            _selectedSubCategory = _initialCategory;
                          });
                          context.read<ProductListingBloc>().add(
                                ProductListingEvent.selectSubcategory(
                                  subCategory: _initialCategory,
                                ),
                              );
                          _initialCategory = null;
                        } else if (subCategory != null) {
                          if (_selectedSubCategory?.id != subCategory.id) {
                            setState(() {
                              _selectedSubCategory = subCategory;
                            });
                            context.read<ProductListingBloc>().add(
                                  ProductListingEvent.selectSubcategory(
                                    subCategory: subCategory,
                                  ),
                                );
                          }
                        }
                      },
                      onSeeAllTap: () {
                        context.push(
                          RouteNames.categories,
                          extra: {
                            'parentCategoryId': widget.category?.parentID,
                            'parentCategoryName': widget.category?.name,
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
              // Products section (3 parts of flex)
              Expanded(
                flex: 4,
                child: _selectedSubCategory == null
                    ? const Center(
                        child: Text('Select a subcategory to view products'))
                    : ProductsSection(
                        title: _selectedSubCategory?.name ?? 'All Products',
                        showSeeAll: false,
                        category: _selectedSubCategory,
                        useGridView: true,
                        imageHeight: 80,
                        imageWidth: 80,
                        padding: EdgeInsets.fromLTRB(
                            AppDimensions.screenHzPadding,
                            12,
                            AppDimensions.screenHzPadding,
                            100),
                        height: MediaQuery.of(context).size.height - 150,
                        gridCrossAxisCount: screenWidth > 600 ? 3 : 2,
                        gridChildAspectRatio: 0.0048,
                        cardWidth:
                            (MediaQuery.of(context).size.width * 0.75) / 2,
                        scrollController: _scrollController,
                        onSeeAllTap: () async {
                          context.push(
                            RouteNames.products,
                            extra: {
                              'category': _selectedSubCategory,
                              'viewAll': true,
                            },
                          );
                          // Log category view event to AppsFlyer
                          await AppsFlyerEvents.categoryView(
                            _selectedSubCategory?.id ?? '',
                            _selectedSubCategory?.name ?? '',
                          );
                        },
                      ),
              ),
            ],
          ),
        ));
  }
}

Future<Object?> showTopCategorySelector(
    BuildContext context,
    ProductListingBloc productBloc,
    HomeBloc homeBloc,
    CategoryEntity? currentCategory,
    CategoryEntity? parentCategory,
    CategoryEntity? selectedSubCategory,
    Function(CategoryEntity?, CategoryEntity?)? onSubcategorySelected) {
  CategoryEntity? selectedCategory = parentCategory;
  final ItemScrollController itemScrollController = ItemScrollController();
  final ScrollOffsetController scrollOffsetController =
      ScrollOffsetController();
  final ItemPositionsListener itemPositionsListener =
      ItemPositionsListener.create();
  final ScrollOffsetListener scrollOffsetListener =
      ScrollOffsetListener.create();

  return showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    barrierColor: Colors.black54, // Overlay color
    pageBuilder: (context, animation, secondaryAnimation) {
      return Align(
        alignment: Alignment.topCenter,
        child: Material(
          type: MaterialType
              .transparency, // Essential for showing content directly
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: EdgeInsets.only(top: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(16.0),
                bottomRight: Radius.circular(16.0),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: Offset(0, 5),
                ),
              ],
            ),
            child: SafeArea(
              // To avoid status bar overlap
              bottom: false,
              child: Column(
                mainAxisSize: MainAxisSize
                    .min, // Important: make it shrink-wrap its children
                children: [
                  BlocProvider.value(
                    value: productBloc,
                    child: BlocBuilder<ProductListingBloc, ProductListingState>(
                      builder: (context, state) {
                        return Column(
                          children: [
                            Row(
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.arrow_back,
                                      color: Colors.black87),
                                  onPressed: () {
                                    context.pop();
                                  },
                                ),
                                SizedBox(width: 24),
                                Text(
                                  currentCategory?.name ?? "Product",
                                  style: const TextStyle(
                                    color: Colors.black87,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            BlocProvider.value(
                              value: homeBloc,
                              child: BlocBuilder<HomeBloc, HomeState>(
                                buildWhen: (previous, current) {
                                  List<CategoryEntity> prevCategories =
                                      previous.mapOrNull(
                                              loaded: (value) =>
                                                  value.categories) ??
                                          [];

                                  List<CategoryEntity> currCategories =
                                      current.mapOrNull(
                                              loaded: (value) =>
                                                  value.categories) ??
                                          [];

                                  return prevCategories != currCategories;
                                },
                                builder: (context, state) {
                                  List<CategoryEntity> categories =
                                      state.maybeMap(
                                          loaded: (value) =>
                                              value.categories ?? [],
                                          orElse: () => []);

                                  int item = categories.indexWhere((e) =>
                                      e.collectionId ==
                                      selectedCategory?.collectionId);

                                  if (item > 1) {
                                    SchedulerBinding.instance
                                        .addPostFrameCallback((_) {
                                      itemScrollController.scrollTo(
                                        index: item,
                                        duration:
                                            const Duration(milliseconds: 500),
                                        curve: Curves.easeInOutCubic,
                                        alignment: 0.35, // Center the item
                                      );
                                    });
                                  }

                                  return StatefulBuilder(
                                      builder: (context, setState) {
                                    return Column(
                                      children: [
                                        SizedBox(
                                          height: 36,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          child: Center(
                                            child: ScrollablePositionedList
                                                .separated(
                                              scrollDirection: Axis.horizontal,
                                              shrinkWrap: true,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: AppDimensions
                                                      .screenHzPadding),
                                              itemCount: categories.length,
                                              itemBuilder: (context, index) {
                                                CategoryEntity category =
                                                    categories[index];

                                                bool isSelected =
                                                    category.collectionId ==
                                                        selectedCategory
                                                            ?.collectionId;

                                                return GestureDetector(
                                                  onTap: () {
                                                    selectedCategory = category;
                                                    setState(() {});
                                                    SchedulerBinding.instance
                                                        .addPostFrameCallback(
                                                            (_) {
                                                      itemScrollController
                                                          .scrollTo(
                                                        index: index,
                                                        duration:
                                                            const Duration(
                                                                milliseconds:
                                                                    500),
                                                        curve: Curves
                                                            .easeInOutCubic,
                                                        alignment:
                                                            0.35, // Center the item
                                                      );
                                                    });
                                                  },
                                                  child: Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 12,
                                                            vertical: 4),
                                                    decoration: BoxDecoration(
                                                      color: isSelected
                                                          ? AppColors.primary
                                                          : AppColors.white,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              50),
                                                      border: Border.all(
                                                          color:
                                                              AppColors.primary,
                                                          width: 1.5),
                                                    ),
                                                    alignment: Alignment.center,
                                                    child: CustomText(
                                                      category.name,
                                                      fontSize: 12,
                                                      color: isSelected
                                                          ? AppColors.white
                                                          : AppColors.primary,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                );
                                              },
                                              separatorBuilder:
                                                  (context, index) =>
                                                      SizedBox(width: 10),
                                              itemScrollController:
                                                  itemScrollController,
                                              scrollOffsetController:
                                                  scrollOffsetController,
                                              itemPositionsListener:
                                                  itemPositionsListener,
                                              scrollOffsetListener:
                                                  scrollOffsetListener,
                                            ),
                                          ),
                                        ),
                                        SubCategoryItem(
                                          currentCategory: currentCategory,
                                          parentCategory: selectedCategory,
                                          level: 'sub_category',
                                          onSubcategorySelected:
                                              onSubcategorySelected,
                                        )
                                      ],
                                    );
                                  });
                                },
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}

class SubCategoryItem extends StatefulWidget {
  final CategoryEntity? currentCategory;
  final CategoryEntity? parentCategory;
  final Function(CategoryEntity?, CategoryEntity?)? onSubcategorySelected;
  final String? level;

  const SubCategoryItem({
    super.key,
    this.onSubcategorySelected,
    this.parentCategory,
    this.level,
    this.currentCategory,
  });

  @override
  State<SubCategoryItem> createState() => _SubCategoryItemState();
}

class _SubCategoryItemState extends State<SubCategoryItem> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void didUpdateWidget(covariant SubCategoryItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.parentCategory?.collectionId !=
        oldWidget.parentCategory?.collectionId) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    if ((widget.parentCategory == null) ||
        (!CategoriesBloc.loadedSubcategories
            .containsKey(widget.parentCategory?.name))) {
      if (_isLoading) return;

      setState(() {
        _isLoading = true;
      });

      try {
        final categories = widget.parentCategory == null
            ? await _dataManager.loadCategories(pageSize: 20)
            : await _dataManager.loadSubCategories(
                category: widget.parentCategory,
                pageSize: 20,
                level: widget.level);

        setState(() {
          _subCategories.clear();
          _subCategories.addAll(categories);
          _isLoading = false;
          CategoriesBloc
                  .loadedSubcategories[widget.parentCategory?.name ?? ''] =
              categories;
        });
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } else {
      _subCategories.clear();
      _subCategories.addAll(CategoriesBloc
              .loadedSubcategories[widget.parentCategory?.name ?? ''] ??
          []);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _isLoading
            ? ListView.builder(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(vertical: 5),
                itemCount: 3,
                itemBuilder: (context, index) => ListTile(
                      leading: ShimmerBox(width: 40, height: 40),
                      title: ShimmerText(height: 14),
                    ))
            : ConstrainedBox(
                constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.7),
                child: ListView.builder(
                  primary: false,
                  shrinkWrap: true,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  itemCount: _subCategories.length,
                  itemBuilder: (context, index) {
                    CategoryEntity subCategory = _subCategories[index];
                    bool isSelected =
                        (widget.currentCategory?.name) == (subCategory.name);

                    return Container(
                      color: isSelected
                          ? AppColors.primary.withValues(alpha: 0.1)
                          : Colors.transparent,
                      child: ListTile(
                        onTap: () {
                          widget.onSubcategorySelected
                              ?.call(widget.parentCategory, subCategory);
                        },
                        leading: CustomImage(
                          imageUrl: subCategory.imageUrl,
                          width: 40,
                          height: 40,
                        ),
                        title: CustomText(subCategory.name),
                      ),
                    );
                  },
                ),
              ),
      ],
    );
  }
}
