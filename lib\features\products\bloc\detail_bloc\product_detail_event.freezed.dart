// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_detail_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductDetailEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ProductDetailEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductDetailEvent()';
  }
}

/// @nodoc
class $ProductDetailEventCopyWith<$Res> {
  $ProductDetailEventCopyWith(
      ProductDetailEvent _, $Res Function(ProductDetailEvent) __);
}

/// Adds pattern-matching-related methods to [ProductDetailEvent].
extension ProductDetailEventPatterns on ProductDetailEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchProduct value)? fetchProduct,
    TResult Function(_SwitchVariant value)? switchVariant,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _FetchProduct() when fetchProduct != null:
        return fetchProduct(_that);
      case _SwitchVariant() when switchVariant != null:
        return switchVariant(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchProduct value) fetchProduct,
    required TResult Function(_SwitchVariant value) switchVariant,
  }) {
    final _that = this;
    switch (_that) {
      case _FetchProduct():
        return fetchProduct(_that);
      case _SwitchVariant():
        return switchVariant(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchProduct value)? fetchProduct,
    TResult? Function(_SwitchVariant value)? switchVariant,
  }) {
    final _that = this;
    switch (_that) {
      case _FetchProduct() when fetchProduct != null:
        return fetchProduct(_that);
      case _SwitchVariant() when switchVariant != null:
        return switchVariant(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String sku, String variantName)? fetchProduct,
    TResult Function(ProductEntity variant)? switchVariant,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _FetchProduct() when fetchProduct != null:
        return fetchProduct(_that.sku, _that.variantName);
      case _SwitchVariant() when switchVariant != null:
        return switchVariant(_that.variant);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String sku, String variantName) fetchProduct,
    required TResult Function(ProductEntity variant) switchVariant,
  }) {
    final _that = this;
    switch (_that) {
      case _FetchProduct():
        return fetchProduct(_that.sku, _that.variantName);
      case _SwitchVariant():
        return switchVariant(_that.variant);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String sku, String variantName)? fetchProduct,
    TResult? Function(ProductEntity variant)? switchVariant,
  }) {
    final _that = this;
    switch (_that) {
      case _FetchProduct() when fetchProduct != null:
        return fetchProduct(_that.sku, _that.variantName);
      case _SwitchVariant() when switchVariant != null:
        return switchVariant(_that.variant);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _FetchProduct implements ProductDetailEvent {
  const _FetchProduct({required this.sku, required this.variantName});

  final String sku;
  final String variantName;

  /// Create a copy of ProductDetailEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FetchProductCopyWith<_FetchProduct> get copyWith =>
      __$FetchProductCopyWithImpl<_FetchProduct>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FetchProduct &&
            (identical(other.sku, sku) || other.sku == sku) &&
            (identical(other.variantName, variantName) ||
                other.variantName == variantName));
  }

  @override
  int get hashCode => Object.hash(runtimeType, sku, variantName);

  @override
  String toString() {
    return 'ProductDetailEvent.fetchProduct(sku: $sku, variantName: $variantName)';
  }
}

/// @nodoc
abstract mixin class _$FetchProductCopyWith<$Res>
    implements $ProductDetailEventCopyWith<$Res> {
  factory _$FetchProductCopyWith(
          _FetchProduct value, $Res Function(_FetchProduct) _then) =
      __$FetchProductCopyWithImpl;
  @useResult
  $Res call({String sku, String variantName});
}

/// @nodoc
class __$FetchProductCopyWithImpl<$Res>
    implements _$FetchProductCopyWith<$Res> {
  __$FetchProductCopyWithImpl(this._self, this._then);

  final _FetchProduct _self;
  final $Res Function(_FetchProduct) _then;

  /// Create a copy of ProductDetailEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sku = null,
    Object? variantName = null,
  }) {
    return _then(_FetchProduct(
      sku: null == sku
          ? _self.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String,
      variantName: null == variantName
          ? _self.variantName
          : variantName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _SwitchVariant implements ProductDetailEvent {
  const _SwitchVariant({required this.variant});

  final ProductEntity variant;

  /// Create a copy of ProductDetailEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SwitchVariantCopyWith<_SwitchVariant> get copyWith =>
      __$SwitchVariantCopyWithImpl<_SwitchVariant>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SwitchVariant &&
            (identical(other.variant, variant) || other.variant == variant));
  }

  @override
  int get hashCode => Object.hash(runtimeType, variant);

  @override
  String toString() {
    return 'ProductDetailEvent.switchVariant(variant: $variant)';
  }
}

/// @nodoc
abstract mixin class _$SwitchVariantCopyWith<$Res>
    implements $ProductDetailEventCopyWith<$Res> {
  factory _$SwitchVariantCopyWith(
          _SwitchVariant value, $Res Function(_SwitchVariant) _then) =
      __$SwitchVariantCopyWithImpl;
  @useResult
  $Res call({ProductEntity variant});
}

/// @nodoc
class __$SwitchVariantCopyWithImpl<$Res>
    implements _$SwitchVariantCopyWith<$Res> {
  __$SwitchVariantCopyWithImpl(this._self, this._then);

  final _SwitchVariant _self;
  final $Res Function(_SwitchVariant) _then;

  /// Create a copy of ProductDetailEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? variant = null,
  }) {
    return _then(_SwitchVariant(
      variant: null == variant
          ? _self.variant
          : variant // ignore: cast_nullable_to_non_nullable
              as ProductEntity,
    ));
  }
}

// dart format on
