export 'product_detail_event.dart';
export 'product_detail_state.dart';

import 'package:bloc/bloc.dart';
import 'package:rozana/domain/entities/product_entity.dart';

import '../../../../data/services/data_loading_manager.dart';
import 'product_detail_event.dart';
import 'product_detail_state.dart';

class ProductDetailBloc extends Bloc<ProductDetailEvent, ProductDetailState> {
  final DataLoadingManager _dataLoadingManager;
  ProductDetailBloc({required DataLoadingManager dataLoadingManager})
      : _dataLoadingManager = dataLoadingManager,
        super(ProductDetailState.loading()) {
    on<ProductDetailEvent>((event, emit) => event.map(
          fetchProduct: (e) async =>
              await _onFetchProduct(e.sku, e.variantName, emit),
          switchVariant: (e) async => await _onSwitcVariant(e.variant, emit),
        ));
  }

  Future<void> _onSwitcVariant(
    ProductEntity variant,
    Emitter<ProductDetailState> emit,
  ) async {
    state.mapOrNull(
      loaded: (value) {
        emit(value.copyWith(selectedVariant: variant));
      },
    );
  }

  Future<void> _onFetchProduct(
    String sku,
    String variantName,
    Emitter<ProductDetailState> emit,
  ) async {
    try {
      // Emit loading state
      final productEntities = await _dataLoadingManager.loadProducts(
        query: '*',
        page: 0,
        pageSize: 1,
        specificSku: sku,
      );

      if (productEntities.isNotEmpty) {
        ProductEntity selectedVariant = productEntities.first;

        if ((variantName.isNotEmpty) &&
            (productEntities.first.variants?.isNotEmpty ?? false)) {
          selectedVariant = (productEntities.first.variants ?? []).firstWhere(
              (e) => e.skuID?.split('-').last == variantName,
              orElse: () =>
                  productEntities.first.variants?.first ??
                  productEntities.first);
        }

        emit(ProductDetailState.loaded(
            product: productEntities.first, selectedVariant: selectedVariant));
      } else {
        emit(ProductDetailState.productError('Product not found'));
      }
    } catch (e) {
      // Emit error state
      emit(ProductDetailState.productError(e.toString()));
    }
  }
}
