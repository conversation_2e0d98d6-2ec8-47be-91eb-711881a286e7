import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';

// Import platform-specific implementation
import 'razorpay_platform_impl.dart';
import '../../../core/services/remote_config_service.dart';

class RazorpayService {
  static final RemoteConfigService _remoteConfig = RemoteConfigService();
  static final String _keyId = _remoteConfig.razorpayKey;

  // Mobile-specific Razorpay instance
  Razorpay? _razorpay;

  // Web-specific implementation
  late final RazorpayWebImpl _webImpl;

  // Callbacks
  final Function(PaymentSuccessResponse) onPaymentSuccess;
  final Function(PaymentFailureResponse) onPaymentError;
  final Function(ExternalWalletResponse)? onExternalWallet;

  RazorpayService({
    required this.onPaymentSuccess,
    required this.onPaymentError,
    this.onExternalWallet,
  }) {
    // Initialize platform-specific implementations
    if (!kIsWeb) {
      _initMobileRazorpay();
    }

    // Initialize web implementation (will be a stub on non-web platforms)
    _webImpl = RazorpayWebImpl(
      onPaymentSuccess: onPaymentSuccess,
      onPaymentError: onPaymentError,
    );
  }

  void _initMobileRazorpay() {
    _razorpay = Razorpay();
    _razorpay!.on(Razorpay.EVENT_PAYMENT_SUCCESS, onPaymentSuccess);
    _razorpay!.on(Razorpay.EVENT_PAYMENT_ERROR, onPaymentError);
    if (onExternalWallet != null) {
      _razorpay!.on(Razorpay.EVENT_EXTERNAL_WALLET, onExternalWallet!);
    }
  }

  void processPayment({
    required String orderId,
    String? name,
    String? description,
    String? email,
    String? contact,
  }) {
    final options = {
      'key': _keyId,
      'order_id': orderId,
      'name': name ?? 'Rozana',
      'description': description ?? 'Payment for your order',
      'prefill': {
        'name': name ?? 'Customer',
        'email': email ?? '',
        'contact': contact ?? '',
      }
    };

    try {
      if (kIsWeb) {
        _webImpl.processPayment(options);
      } else {
        _razorpay?.open(options);
      }
    } catch (e) {
      debugPrint('Error: $e');
      onPaymentError(PaymentFailureResponse(
        0,
        'Error: $e',
        null,
      ));
    }
  }

  void dispose() {
    if (!kIsWeb) {
      _razorpay?.clear();
    }
  }
}
