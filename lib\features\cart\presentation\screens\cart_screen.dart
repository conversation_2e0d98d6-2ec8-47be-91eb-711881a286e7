import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/features/auth/presentation/views/login_bottom_sheet.dart';
import 'package:rozana/features/cart/presentation/widgets/cart_section_card.dart';
import 'package:rozana/features/cart/presentation/widgets/payment_confirmation_screen.dart';
import 'package:rozana/features/location/services/adress_services.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_event.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/features/cart/bloc/free_product_bloc/free_product_bloc.dart';
import 'package:rozana/features/cart/bloc/free_product_bloc/free_product_event.dart';
import 'package:rozana/features/cart/bloc/free_product_bloc/free_product_state.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/features/search/services/typesense_service.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/services/remote_config_service.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/extensions/localization_extension.dart';
import '../../../../core/utils/color_utils.dart';
import '../../../auth/bloc/login_bloc/login_bloc.dart';
import '../../../location/bloc/location bloc/location_bloc.dart';
import '../../../search/presentation/screens/search_screen.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/empty_cart.dart';
import '../widgets/payment_method_selector.dart';
import '../widgets/free_product_card.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartBloc _cartBloc = getIt<CartBloc>();
  final FreeProductBloc _freeProductBloc = FreeProductBloc();
  final TextEditingController _couponController = TextEditingController();
  String _paymentMethod = 'cod';

  @override
  void initState() {
    super.initState();
    themeSettings =
        (RemoteConfigService().getThemeConfig['cart']?.isNotEmpty ?? false)
            ? RemoteConfigService().getThemeConfig['cart']
            : themeSettings;
    _cartBloc.add(CartEvent.init());

    // Listen to cart changes to trigger free product checks
    _cartBloc.stream.listen((cartState) {
      final total = cartState.cart.total;
      if (total != null && total > 0) {
        _freeProductBloc.add(FreeProductEvent.checkOffer(
          cartAmount: total.toDouble(),
        ));
      }
    });

    // Load default address if user is authenticated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appBloc = getIt<AppBloc>();
      if (appBloc.isAuthenticated) {
        _cartBloc.add(CartEvent.loadDefaultAddress());
      }
      _searchProductsByCartAmount();
    });
  }

  Future<void> _searchProductsByCartAmount() async {
    try {
      final typesenseService = getIt<TypesenseService>();
      final result = await typesenseService.getFreeProductsByCartAmount(
        cartAmount: 3000,
        pageSize: 10,
      );

      if (result != null) {
        final productId = result['productId'] as String?;
        final variantId = result['variantId'] as String?;
        if (productId != null && variantId != null) {
          final products = await typesenseService.searchProducts(
            productId: productId,
            variantId: variantId,
            pageSize: 1,
          );
          debugPrint(products.toString());
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  @override
  void dispose() {
    _freeProductBloc.close();
    _couponController.dispose();
    super.dispose();
  }

  Map<String, dynamic> themeSettings = {
    "background_color": "#F0F0F6",
    "topbar_color": "#FFFFFF",
    // "highlight_color": "#BEABD3",
    // "divider_color": "#5C2C90",
    "icon_primary_color": "#371A56",
    "icon_secondary_color": "#4A2373"
  };

  @override
  Widget build(BuildContext context) {
    final Color iconPrimaryColor =
        ColorUtils.hexToColor(themeSettings['icon_primary_color']) ??
            AppColors.primary700;

    final Color iconSecondaryColor =
        ColorUtils.hexToColor(themeSettings['icon_secondary_color']) ??
            AppColors.primary600;
    return BlocProvider<FreeProductBloc>(
      create: (context) => _freeProductBloc,
      child: MultiBlocListener(
        listeners: [
          BlocListener<AppBloc, AppState>(
            listener: (context, state) {
              state.maybeMap(
                loaded: (loaded) {
                  if (loaded.isAuthenticated) {
                    // Load default address when user logs in
                    _cartBloc.add(CartEvent.loadDefaultAddress());
                  } else {
                    // Clear address when user logs out
                    _cartBloc.add(CartEvent.clearAddress());
                  }
                },
                orElse: () {},
              );
            },
          ),
          BlocListener<CartBloc, CartState>(
            listenWhen: (previous, current) =>
                previous.orderStatus != current.orderStatus,
            listener: (context, state) {
              // Handle order status changes
              switch (state.orderStatus) {
                case OrderProcessingStatus.success:
                  // Navigate to order success screen
                  if (state.orderId != null && state.orderData != null) {
                    context.go(
                      RouteNames.orderSuccess,
                      extra: {
                        'orderId': state.orderId,
                        'orderData': state.orderData,
                      },
                    );
                  }
                  break;
                case OrderProcessingStatus.verifyingPayment:
                  final bool paymentCancelled = state.orderData != null &&
                      state.orderData!['paymentCancelled'] == true;

                  if (!paymentCancelled) {
                    Navigator.of(context).push(
                      PageRouteBuilder(
                        opaque: true,
                        pageBuilder: (context, _, __) =>
                            PaymentConfirmationScreen(
                          initialState: PaymentAnimationState.processing,
                        ),
                        transitionsBuilder:
                            (context, animation, secondaryAnimation, child) {
                          return FadeTransition(
                              opacity: animation, child: child);
                        },
                      ),
                    );
                  }
                  break;
                case OrderProcessingStatus.error:
                  Navigator.of(context, rootNavigator: true)
                      .popUntil((route) => route.isFirst);

                  if (state.error != null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('${context.l10n.error}: ${state.error}'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                  break;
                default:
                  break;
              }
            },
          ),
        ],
        child: Scaffold(
          backgroundColor:
              ColorUtils.hexToColor(themeSettings['background_color']) ??
                  AppColors.neutral150,
          appBar: AppBar(
            backgroundColor: AppColors.neutral100,
            scrolledUnderElevation: 0,
            elevation: 0,
            centerTitle: false,
            titleSpacing: 0,
            leadingWidth: 0,
            automaticallyImplyLeading: false,
            title: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(14, 6, 6, 6),
                    child: Image.asset(
                      'assets/new/icons/chevron_left.png',
                      height: 26,
                      width: 26,
                      color: iconSecondaryColor,
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: CustomText(
                    "Cart",
                    color: iconPrimaryColor,
                    fontSize: 20,
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ],
            ),
          ),
          body: BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) =>
                previous.isLoading != current.isLoading,
            builder: (context, state) {
              if (state.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              return BlocBuilder<CartBloc, CartState>(
                buildWhen: (previous, current) =>
                    previous.cart.items != current.cart.items,
                builder: (context, state) {
                  if (state.cart.items?.isEmpty ?? true) {
                    return const EmptyCart();
                  }

                  return _buildCartContentOptimized(context);
                },
              );
            },
          ),
          bottomNavigationBar: BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) =>
                previous.cart.items != current.cart.items ||
                previous.cart.total != current.cart.total ||
                previous.cart.totalItems != current.cart.totalItems,
            builder: (context, state) {
              if (state.cart.items?.isEmpty ?? true) {
                return const SizedBox();
              }

              return _buildCheckoutBar(context);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCartContentOptimized(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) =>
                (current.stockStatus != StockAvailableStatus.quantityChecking &&
                    (previous.stockStatus != current.stockStatus)) ||
                (previous.cart.items?.length != current.cart.items?.length),
            builder: (context, state) {
              List<CartItemModel>? availableItems = [];
              List<CartItemModel>? unAvailableItems = [];

              state.cart.items?.forEach((item) {
                if ((item.availableQuantity ?? 0) > 0) {
                  availableItems.add(item);
                } else {
                  unAvailableItems.add(item);
                }
              });
              return Column(
                children: [
                  Visibility(
                    visible:
                        state.stockStatus == StockAvailableStatus.outOfStock,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: CartSectionCard(
                        title: CustomText(
                          '${unAvailableItems.length} ${(unAvailableItems.length) > 1 ? "items are" : "item is"} not in stock',
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.red600,
                        ),
                        trailing: InkWell(
                          onTap: () {
                            for (var e in unAvailableItems) {
                              getIt<CartBloc>().add(CartEvent.removeItem(
                                  e.id ?? '', e.skuID ?? ''));
                            }
                            getIt<CartBloc>()
                                .add(CartEvent.checkAvailability());
                          },
                          child: SizedBox(
                            height: 36.sp,
                            width: 36.sp,
                            child: Center(
                              child: Image.asset(
                                'assets/new/icons/cancel.png',
                                height: 24.sp,
                                width: 24.sp,
                                color: AppColors.red600,
                              ),
                            ),
                          ),
                        ),
                        children: [
                          ListView.separated(
                              primary: false,
                              shrinkWrap: true,
                              itemBuilder: (ctx, index) {
                                CartItemModel? item = unAvailableItems[index];
                                return CartItemCard(
                                  key: ValueKey(item.id),
                                  item: item,
                                  isEditable: false,
                                );
                              },
                              separatorBuilder: (ctx, index) => Divider(
                                    thickness: 0.5,
                                    color: AppColors.neutral150,
                                    height: 0.5,
                                  ),
                              itemCount: unAvailableItems.length),
                        ],
                      ),
                    ),
                  ),
                  CartSectionCard(
                    logo: 'assets/new/icons/moped.png',
                    title: BlocBuilder<LocationBloc, LocationState>(
                      builder: (context, state) {
                        String? durationText;
                        state.maybeWhen(
                          loaded: (_, duration) {
                            durationText = duration;
                          },
                          orElse: () {},
                        );
                        String displayText = durationText?.isNotEmpty ?? false
                            ? 'Delivery in $durationText'
                            : 'Delivery in 1hr';
                        return CustomText(
                          displayText,
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.primary700,
                        );
                      },
                    ),
                    subTitle:
                        '${availableItems.length} ${(availableItems.length) > 1 ? "items" : "item"} for delivery',
                    children: [
                      ListView.separated(
                          primary: false,
                          shrinkWrap: true,
                          itemBuilder: (ctx, index) {
                            CartItemModel? item = availableItems[index];
                            return CartItemCard(
                              key: ValueKey(item.id),
                              item: item,
                            );
                          },
                          separatorBuilder: (ctx, index) => Divider(
                                thickness: 0.5,
                                color: AppColors.neutral150,
                                height: 0.5,
                              ),
                          itemCount: availableItems.length),
                    ],
                  ),
                ],
              );
            },
          ),

          // Free Product Selection Section
          BlocBuilder<FreeProductBloc, FreeProductState>(
            builder: (context, freeProductState) {
              if (freeProductState.hasActiveOffer &&
                  freeProductState.freeProducts.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Available Free Products',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.neutral700,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...freeProductState.freeProducts.map(
                      (product) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: FreeProductCard(product: product),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),

          // Selected Free Products Section
          BlocBuilder<FreeProductBloc, FreeProductState>(
            builder: (context, freeProductState) {
              if (freeProductState.selectedFreeProducts.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.success.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.card_giftcard,
                                  color: AppColors.success,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Your Free Products',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.success,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            ...freeProductState.selectedFreeProducts.map(
                              (product) => Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      color: AppColors.success,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        product.name,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: AppColors.neutral700,
                                        ),
                                      ),
                                    ),
                                    Text(
                                      'FREE',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.success,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),

          // const SizedBox(height: 16),

          // Coupon section
          // _buildCouponSection(),

          const SizedBox(height: 16),

          // Cart summary with Selector to update when cart totals change
          BlocBuilder<CartBloc, CartState>(
              buildWhen: (previous, current) => previous.cart != current.cart,
              builder: (context, state) {
                num itemsTotalMRP = 0;
                num itemsDiscountedPrice = 0;

                state.cart.items?.forEach((e) {
                  itemsTotalMRP =
                      itemsTotalMRP + ((e.price ?? 0) * (e.quantity ?? 1));
                  itemsDiscountedPrice = itemsDiscountedPrice +
                      ((e.discountedPrice ?? 0) * (e.quantity ?? 1));
                });

                return CartSectionCard(
                  logo: 'assets/new/icons/receipt_long.png',
                  titleText: "Bill details",
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.sp),
                      child: Column(
                        children: [
                          BillDetailRow(
                            logo: 'assets/new/icons/local_mall.png',
                            title: 'Items total',
                            customTag: Visibility(
                              visible:
                                  (itemsTotalMRP - itemsDiscountedPrice) > 0,
                              child: Container(
                                margin: EdgeInsets.only(right: 6),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppColors.blue100,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: CustomText(
                                  'Saved ₹${formattedPrice(itemsTotalMRP - itemsDiscountedPrice)}',
                                  fontSize: 10,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.blue600,
                                ),
                              ),
                            ),
                            originalPrice: itemsTotalMRP,
                            discountedPrice: itemsDiscountedPrice,
                          ),
                          BillDetailRow(
                            logo: 'assets/new/icons/moped.png',
                            title: 'Delivery charge',
                            showBaseLine: true,
                            customTag: Visibility(
                              visible: (state.cart.deliveryFee ?? 0) <= 0,
                              child: Container(
                                margin: EdgeInsets.only(right: 6),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppColors.green100,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: CustomText(
                                  'Free delivery',
                                  fontSize: 10,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.green600,
                                ),
                              ),
                            ),
                            originalPrice: state.cart.deliveryFee,
                            discountedPrice: state.cart.deliveryFee,
                          ),
                          BillDetailRow(
                            logo: 'assets/new/icons/receipt.png',
                            title: 'Taxes',
                            originalPrice: state.cart.tax,
                            discountedPrice: state.cart.tax,
                          ),
                          SizedBox(height: 12),
                        ],
                      ),
                    ),
                    Divider(
                      thickness: 0.5,
                      color: AppColors.neutral150,
                      height: 0.5,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                      child: Row(
                        children: [
                          CustomText(
                            'Grand total',
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            color: AppColors.neutral600,
                            textHeight: 1.4,
                          ),
                          Spacer(),
                          SizedBox(width: 12),
                          CustomText(
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            '₹${formattedPrice(state.cart.total)}',
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            textHeight: 1.4,
                            color: AppColors.neutral700,
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              }),

          const SizedBox(height: 12),
          CartSectionCard(
              logo: 'assets/new/icons/receipt_long.png',
              titleText: "Pay using",
              children: [
                PaymentMethodSelector(
                  selectedMethod: _paymentMethod,
                  onMethodSelected: (method) {
                    setState(() {
                      _paymentMethod = method;
                    });
                  },
                )
              ]),

          const SizedBox(height: 100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildCheckoutBar(BuildContext context) {
    return BlocBuilder<LocationBloc, LocationState>(
      buildWhen: (previous, current) =>
          previous.maybeMap(orElse: () => false, loading: (value) => true) ||
          current.maybeMap(orElse: () => false, loading: (value) => true),
      builder: (context, locationState) {
        bool isLocationLoading = locationState.maybeMap(
            orElse: () => false, loading: (value) => true);
        return BlocBuilder<AppBloc, AppState>(
          builder: (context, appState) {
            return BlocBuilder<CartBloc, CartState>(
              builder: (context, cartState) {
                final isAuthenticated = appState.maybeMap(
                  loaded: (loaded) => loaded.isAuthenticated,
                  orElse: () => false,
                );

                AddressModel? address = cartState.deliveryAddress;

                StockAvailableStatus stockStatus = cartState.stockStatus;

                // Determine button text and action based on authentication, address status and order processing
                String buttonText;
                VoidCallback? onPressed;

                if (isLocationLoading) {
                  buttonText = 'Fetching address...';
                  onPressed = null;
                } else if (cartState.orderStatus ==
                    OrderProcessingStatus.processing) {
                  buttonText = 'Processing...';
                  onPressed = null;
                } else if (!isAuthenticated) {
                  buttonText = 'Login to proceed';
                  // context.l10n.loginToProceed;
                  onPressed = () async {
                    // Log initiated checkout event to AppsFlyer

                    await AppsFlyerEvents.initiatedCheckout(
                      cartItems: cartState.cart.items,
                      totalPrice: cartState.cart.total?.toDouble(),
                    );
                    _placeOrder(cartState);
                  };
                } else if (cartState.deliveryAddress == null) {
                  buttonText = 'Select address at next step';
                  // context.l10n.selectAddress;
                  onPressed = () async {
                    // Log initiated checkout event to AppsFlyer
                    await AppsFlyerEvents.initiatedCheckout(
                      cartItems: cartState.cart.items,
                      totalPrice: cartState.cart.total?.toDouble(),
                    );
                    await _navigateToAddressSelection();
                  };
                } else {
                  buttonText = context.l10n.placeOrder;
                  onPressed = () => _placeOrder(cartState);
                }

                return Container(
                  padding: const EdgeInsets.fromLTRB(16, 10, 16, 12),
                  decoration: BoxDecoration(
                    color: AppColors.neutral100,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.06),
                        blurRadius: 2,
                        spreadRadius: 0,
                        offset: const Offset(0, -1),
                      ),
                    ],
                  ),
                  child: SafeArea(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Visibility(
                          visible: isAuthenticated && (address != null),
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 10),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Image.asset(
                                      (address?.addressType == 'home')
                                          ? 'assets/new/icons/house.png'
                                          : (address?.addressType == 'work')
                                              ? 'assets/new/icons/corporate_fare.png'
                                              : 'assets/new/icons/pin_drop.png',
                                      width: 20,
                                      height: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              CustomText(
                                                'Delivering to',
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.neutral600,
                                                textHeight: 1.4,
                                              ),
                                              SizedBox(width: 3),
                                              CustomText(
                                                capitalizeFirstLetter(
                                                    address?.addressType ??
                                                        'Address'),
                                                fontSize: 16,
                                                fontWeight: FontWeight.w700,
                                                color: AppColors.primary700,
                                              ),
                                            ],
                                          ),
                                          CustomText(
                                            address?.fullAddress ?? '',
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.neutral500,
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    TextButton(
                                      onPressed: () {
                                        HapticFeedback.mediumImpact();
                                        _navigateToAddressSelection();
                                      },
                                      style: ButtonStyle(
                                          padding: WidgetStatePropertyAll(
                                              EdgeInsets.fromLTRB(
                                                  16, 10, 0, 10))),
                                      child: IntrinsicWidth(
                                        child: Column(
                                          children: [
                                            CustomText(
                                              'Change',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w700,
                                              color: AppColors.primary600,
                                              textHeight: 1.6,
                                            ),
                                            SizedBox(
                                              width: double.infinity,
                                              child: DottedLine(
                                                color: AppColors.primary600,
                                                height: 1.5,
                                                dashWidth: 4,
                                                dashSpace: 4,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(height: 8),
                                Divider(
                                  thickness: 0.5,
                                  color: AppColors.neutral150,
                                  height: 0.5,
                                ),
                                SizedBox(height: 8),
                              ],
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            Visibility(
                              visible: isAuthenticated && (address != null),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                    maxWidth:
                                        MediaQuery.of(context).size.width *
                                            0.3),
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Image.asset(
                                            _paymentMethod == 'cod'
                                                ? 'assets/new/icons/payment-methods.png'
                                                : 'assets/new/icons/payment-methods_2.png',
                                            height: 24,
                                            width: 34,
                                          ),
                                          SizedBox(width: 4),
                                          CustomText(
                                            'PAY USING',
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.neutral500,
                                          ),
                                        ],
                                      ),
                                      CustomText(
                                        _paymentMethod == 'cod'
                                            ? 'Cash on delivery'
                                            : 'UPI / Credit, Debit card / Netbanking',
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.neutral700,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: (stockStatus ==
                                            StockAvailableStatus.inStock ||
                                        !(isAuthenticated) ||
                                        (address == null))
                                    ? onPressed
                                    : null,
                                child: Container(
                                  height: 48.sp,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 5),
                                  decoration: BoxDecoration(
                                      color: (stockStatus ==
                                                  StockAvailableStatus
                                                      .inStock ||
                                              !(isAuthenticated) ||
                                              (address == null))
                                          ? AppColors.primary
                                          : AppColors.primary100,
                                      borderRadius: BorderRadius.circular(12)),
                                  child: Row(
                                    children: [
                                      Visibility(
                                        visible: isAuthenticated &&
                                            (address != null),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            CustomText(
                                              '₹${(cartState.cart.total ?? 0).toStringAsFixed(2)}',
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700,
                                              color: AppColors.neutral100,
                                              textHeight: 1.2,
                                            ),
                                            CustomText(
                                              'Total',
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: AppColors.primary100,
                                              textHeight: 1.2,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: Center(
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              CustomText(
                                                buttonText,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.neutral150,
                                              ),
                                              Icon(
                                                Icons.arrow_right,
                                                color: AppColors.neutral150,
                                              )
                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Future<void> _navigateToAddressSelection() async {
    // Check if user has any existing addresses
    final addressService = AddressService();
    final addresses = await addressService.getAllAddresses();

    if (!mounted) return;

    if (addresses.isEmpty) {
      // No addresses exist, go directly to add address form
      final newAddress = await context.push<AddressModel>(
        RouteNames.mapForNewAddress,
        extra: {'fromCart': true},
      );

      // After returning from address form, select the new address
      if (mounted && newAddress != null) {
        _cartBloc.add(CartEvent.selectAddress(newAddress));
      }
    } else {
      final selectedAddress = await context.push<AddressModel>(
        RouteNames.addresses,
        extra: {
          'selectMode': true,
          'onAddressSelected': (AddressModel address) {
            context.pop(address);
          },
        },
      );

      if (mounted && selectedAddress != null) {
        _cartBloc.add(CartEvent.selectAddress(selectedAddress));
      }
    }
  }

  void _showLoginBottomSheet(BuildContext context) {
    final LoginBloc loginBloc = getIt<LoginBloc>();
    loginBloc.add(LoginEvent.initLogin(showHint: false));

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (ctx) {
        return BlocListener<AppBloc, AppState>(
          listenWhen: (previous, current) =>
              previous.maybeMap(
                  orElse: () => false,
                  loaded: (value) => value.isAuthenticated) !=
              current.maybeMap(
                  orElse: () => false,
                  loaded: (value) => value.isAuthenticated),
          listener: (context, state) {
            state.mapOrNull(
              loaded: (value) {
                if (value.isAuthenticated) {
                  context.pop();
                }
              },
            );
          },
          child: BlocProvider.value(
            value: loginBloc,
            child: LoginBottomSheet(),
          ),
        );
      },
    );
  }

  // Trigger order placement through BLoC
  void _placeOrder(CartState state) {
    if (state.cart.items?.isEmpty ?? true) return;

    // Check if user is authenticated before proceeding with checkout
    final appBloc = getIt<AppBloc>();
    if (!appBloc.isAuthenticated) {
      _showLoginBottomSheet(context);
      // Navigate to login screen with return route information
      // context.push(RouteNames.login, extra: {'returnRoute': RouteNames.cart});
      return;
    }

    // Dispatch place order event to the BLoC
    _cartBloc.add(CartEvent.placeOrder(paymentMethod: _paymentMethod));
  }
}

class BillDetailRow extends StatelessWidget {
  const BillDetailRow({
    super.key,
    required this.logo,
    required this.title,
    this.customTag,
    required this.originalPrice,
    required this.discountedPrice,
    this.margin,
    this.bottomWidget,
    this.showBaseLine = false,
  });

  final String logo;
  final String title;
  final Widget? customTag;
  final num? originalPrice;
  final num? discountedPrice;
  final EdgeInsetsGeometry? margin;
  final Widget? bottomWidget;
  final bool showBaseLine;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin ?? const EdgeInsets.only(top: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SizedBox.square(
                      dimension: 14.sp,
                      child: Image.asset(
                        logo,
                        color: AppColors.neutral600,
                      ),
                    ),
                    SizedBox(width: 6),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          title,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral600,
                          textHeight: 1.4,
                        ),
                        Visibility(
                          visible: showBaseLine,
                          child: SizedBox(
                            width: 95,
                            child: DottedLine(
                              color: AppColors.neutral600,
                              height: 1.5,
                              dashWidth: 1.5,
                              dashSpace: 2,
                            ),
                          ),
                        )
                      ],
                    ),
                    SizedBox(width: 6),
                    customTag ?? SizedBox(),
                  ],
                ),
                bottomWidget ?? SizedBox()
              ],
            ),
          ),
          SizedBox(width: 12),
          Row(
            children: [
              Visibility(
                visible: (originalPrice ?? 0) > (discountedPrice ?? 0),
                child: IntrinsicWidth(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CustomText(
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        '₹${formattedPrice(originalPrice)}',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textHeight: 1.4,
                        color: AppColors.neutral400,
                      ),
                      Transform.rotate(
                        angle: 2.9,
                        child: Container(
                          height: 1,
                          width: double.infinity,
                          color: AppColors.neutral400,
                        ),
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(width: 6),
              CustomText(
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                '₹${formattedPrice(discountedPrice)}',
                fontSize: 14,
                fontWeight: FontWeight.w700,
                textHeight: 1.4,
                color: AppColors.neutral700,
              ),
              SizedBox(width: 2),
            ],
          ),
        ],
      ),
    );
  }
}

String formattedPrice(num? price) {
  String formattedPrice;
  if (price != null) {
    // Always format to two decimal places first
    String priceString = price.toStringAsFixed(2);

    // Check if the formatted string ends with ".00"
    if (priceString.endsWith('.00')) {
      // If it does, remove the ".00" part
      formattedPrice = priceString.substring(0, priceString.length - 3);
    } else {
      // Otherwise, use the original formatted string
      formattedPrice = priceString;
    }
  } else {
    formattedPrice = '0'; // Or some default value
  }

  return formattedPrice;
}

String capitalizeFirstLetter(String text) {
  if (text.isEmpty) {
    return text;
  }
  return text[0].toUpperCase() + text.substring(1);
}
