import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../data/models/cart_item_model.dart';
import '../../../data/models/adress_model.dart';
part 'cart_event.freezed.dart';

@freezed
class CartEvent with _$CartEvent {
  const factory CartEvent.init() = CartInit;
  const factory CartEvent.addItem(
      {required CartItemModel item, @Default('') String screen}) = CartAddItem;

  const factory CartEvent.removeItem(String itemId, String sku) =
      CartRemoveItem;
  const factory CartEvent.updateQuantity(
      String itemId, String sku, int quantity,
      {@Default('') String screen}) = CartUpdateQuantity;
  const factory CartEvent.clear() = CartClear;
  const factory CartEvent.applyCoupon(String code) = CartApplyCoupon;
  const factory CartEvent.removeCoupon() = CartRemoveCoupon;
  const factory CartEvent.importData(String jsonData) = CartImportData;

  // Address related events
  const factory CartEvent.loadDefaultAddress() = CartLoadDefaultAddress;
  const factory CartEvent.selectAddress(AddressModel address) =
      CartSelectAddress;
  const factory CartEvent.clearAddress() = CartClearAddress;

  // Order creation event
  const factory CartEvent.placeOrder({required String paymentMethod}) =
      CartPlaceOrder;

  // Order completion event
  const factory CartEvent.completeOrder({
    required String orderId,
    required Map<String, dynamic> orderData,
  }) = CartCompleteOrder;

  // Payment failure event
  const factory CartEvent.paymentFailed(String errorMessage) =
      CartPaymentFailed;

  // Payment verification event
  const factory CartEvent.verifyPayment({
    required String razorpayOrderId,
    required String razorpayPaymentId,
    required String razorpaySignature,
    required String orderId,
    required Map<String, dynamic> orderData,
  }) = CartVerifyPayment;

  const factory CartEvent.checkAvailability({List<String>? skus}) =
      CartCheckAvailability;
}
