import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/routes/app_router.dart';
import '../../services/typesense_service.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/data/mappers/product_mapper.dart';
import 'package:rozana/domain/entities/product_entity.dart';
import 'package:rozana/features/home/<USER>/home%20bloc/home_bloc.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';
import 'package:rozana/features/products/presentation/widgets/product_section.dart';
import 'package:rozana/widgets/barcode_scanner/barcode_scanner.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/custom_textfield.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../data/models/product_model.dart';
import '../../../cart/presentation/widgets/floating_cart_wrapper.dart';
import '../../../home/<USER>/widgets/section_most_bought.dart';
import '../../bloc/bloc/search_bloc.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({
    super.key,
  });

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  Future<void> _openBarcodeScanner(BuildContext context) async {
    final scannedValue = await ScannerController.scan(
        context: context,
        title: 'Scan EAN Barcode',
        description: 'Scan EAN barcode to check product details');
    if (scannedValue != null && mounted) {
      try {
        final results =
            await getIt<TypesenseService>().eanSearch(query: scannedValue);
        final products = results['products'] ?? [];
        if (products.isNotEmpty) {
          final firstProduct = products[0];
          final String sku = firstProduct['parentSku'] ??
              firstProduct['sku'] ??
              firstProduct['skuID']?.toString().split('-').first ??
              '';
          final String variantName =
              firstProduct['skuID']?.toString().split('-').last ?? '';

          if (sku.isNotEmpty) {
            context.pushNamed(
              RouteNames.productDetail,
              pathParameters: {'id': sku},
              extra: {
                'sku': sku,
                'variant_name': variantName,
              },
            );
            return;
          }
        }
      } catch (e) {
        debugPrint(e.toString());
      }
    } else {
      context.read<SearchBloc>().add(const SearchEvent.clearSearch());
    }
  }

  @override
  Widget build(BuildContext context) {
    SearchBloc searchBloc = context.read<SearchBloc>();
    return Scaffold(
      backgroundColor: AppColors.neutral100,
      appBar: AppBar(
        backgroundColor: AppColors.blue100,
        scrolledUnderElevation: 0,
        elevation: 0,
        titleSpacing: 0,
        toolbarHeight: 70,
        automaticallyImplyLeading: false, // Remove default leading
        title: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 0, 0, 10),
          child: Row(
            children: [
              Expanded(
                child: SearchTextField(
                  controller: SearchBloc.searchFieldManager?.controller,
                  focusNode: SearchBloc.searchFieldManager?.focusNode,
                  hintText: 'Search milk, shirt, soap and more',
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9\s]')),
                  ],
                  autofocus: true,
                  onChanged: (query) =>
                      searchBloc.add(SearchEvent.inputChange(query)),
                  onSubmitted: (query) =>
                      searchBloc.add(SearchEvent.search(query, submit: true)),
                  onClear: () =>
                      searchBloc.add(const SearchEvent.clearSearch()),
                  onBackPressed: () => context.pop(),
                ),
              ),
              const SizedBox(width: 6),
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: InkWell(
                  onTap: () => _openBarcodeScanner(context),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    height: 58,
                    width: 58,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border:
                          Border.all(color: AppColors.primary200, width: 0.5),
                    ),
                    child: Center(
                      child: Image.asset(
                        'assets/new/icons/scan.png',
                        height: 28,
                        width: 28,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.qr_code_scanner,
                          color: AppColors.primary500,
                          size: 28,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      body: BlocBuilder<SearchBloc, SearchState>(
        builder: (context, state) {
          // return SearchBody(state: state);

          return NotificationListener<ScrollNotification>(
            onNotification: (scrollInfo) {
              if ((scrollInfo.metrics.maxScrollExtent > 0) &&
                  (scrollInfo.metrics.pixels >=
                      scrollInfo.metrics.maxScrollExtent * 0.8) &&
                  state.hasMore &&
                  !state.isLoading) {
                searchBloc.add(const SearchEvent.loadMore());
              }
              return false;
            },
            child: SearchBody(state: state),
          );
        },
      ),
    );
  }
}

class SearchBody extends StatelessWidget {
  const SearchBody({super.key, required this.state});

  final SearchState state;

  @override
  Widget build(BuildContext context) {
    if (state.suggestions.isNotEmpty || state.isLoading) {
      return LayoutBuilder(builder: (context, constraints) {
        return CustomScrollView(
          slivers: [
            // Suggestions list will be shown if available
            if (state.suggestions.isNotEmpty || state.isLoading)
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final suggestion =
                        state.isLoading ? {} : state.suggestions[index];
                    return ColoredBox(
                      color: AppColors.blue100,
                      child: ListTile(
                        leading: state.isLoading
                            ? CustomShimmer(
                                height: 40,
                                width: 40,
                                baseColor:
                                    Color(0xFFB2A9E6).withValues(alpha: 0.3),
                                highlightColor: Color(0xFFD8D5EA),
                              )
                            : Container(
                                decoration: BoxDecoration(
                                  color: AppColors.neutral100,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      color: AppColors.primary100, width: 0.5),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Padding(
                                    padding: const EdgeInsets.all(2),
                                    child: CustomImage(
                                      imageUrl: suggestion['imageUrl'],
                                      width: 40,
                                      height: 40,
                                      imageType: 'product',
                                      fallbackImage:
                                          'assets/new/icons/search.png',
                                    ),
                                  ),
                                ),
                              ),
                        title: state.isLoading
                            ? Padding(
                                padding: const EdgeInsets.only(right: 100),
                                child: CustomShimmer(
                                  height: 22,
                                  baseColor:
                                      Color(0xFFB2A9E6).withValues(alpha: 0.3),
                                  highlightColor: Color(0xFFD8D5EA),
                                ),
                              )
                            : CustomText(
                                suggestion['display_alias'] ??
                                    suggestion['name'],
                                overflow: TextOverflow.ellipsis,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: AppColors.primary500,
                              ),
                        onTap: () => context.read<SearchBloc>().add(
                              SearchEvent.selectSuggestion(
                                  suggestion['name'], suggestion['sku']),
                            ),
                      ),
                    );
                  },
                  childCount: state.isLoading ? 4 : state.suggestions.length,
                ),
              ),

            // Search results list with pagination
            if (state.products.isNotEmpty || state.isLoading)
              SliverPadding(
                padding: EdgeInsetsGeometry.fromLTRB(
                    AppDimensions.screenHzPadding,
                    12,
                    AppDimensions.screenHzPadding,
                    100),
                sliver: SliverGrid(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3, // 👈 Set the number of columns here
                    mainAxisSpacing: constraints.maxWidth * 0.025,
                    crossAxisSpacing: constraints.maxWidth * 0.025,

                    mainAxisExtent: (constraints.maxWidth /
                            (constraints.maxWidth * (0.005)))
                        .sp,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      if (state.isLoading) {
                        return ProductCardShimmer();
                      }
                      // Your code to build a grid item for each product
                      final product = state.products[index];
                      ProductModel model = ProductModel.fromJson(product);
                      ProductEntity entity = ProductMapper.toEntity(model);
                      // Use your existing ProductCard or similar widget here
                      return DiscountedProductCard(
                          product: entity, isLoading: state.isLoading);
                    },
                    childCount: state.isLoading ? 6 : state.products.length,
                  ),
                ),
              ),

            // Loading indicator for pagination
            // if (state.isLoading && state.products.isNotEmpty)
            //   const SliverToBoxAdapter(
            //     child: Padding(
            //       padding: EdgeInsets.symmetric(vertical: 16.0),
            //       child: Center(child: CircularProgressIndicator()),
            //     ),
            //   ),

            if (!state.isLoading && state.products.isEmpty)
              SliverToBoxAdapter(
                child: NoProductsMessage(
                  query: state.query,
                ),
              )
          ],
        );
      });
    }

    if (state.products.isEmpty && state.query.isEmpty) {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (state.recentSearches.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(
                    AppDimensions.screenHzPadding,
                    12,
                    AppDimensions.screenHzPadding,
                    0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      'Recent searches',
                      fontSize: 20,
                      fontWeight: FontWeight.w800,
                      color: AppColors.primary700,
                    ),
                    TextButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        context
                            .read<SearchBloc>()
                            .add(const SearchEvent.clearRecent());
                      },
                      style: ButtonStyle(
                          padding: WidgetStatePropertyAll(
                              EdgeInsets.fromLTRB(16, 8, 0, 8))),
                      child: IntrinsicWidth(
                        child: Column(
                          children: [
                            CustomText(
                              'Clear',
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              color: AppColors.primary500,
                              textHeight: 1.6,
                            ),
                            SizedBox(
                              width: double.infinity,
                              child: DottedLine(
                                color: AppColors.primary500,
                                height: 1.5,
                                dashWidth: 4,
                                dashSpace: 4,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            if (state.recentSearches.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(
                    AppDimensions.screenHzPadding,
                    8,
                    AppDimensions.screenHzPadding,
                    12),
                child: Wrap(
                  runSpacing: 8,
                  spacing: 8,
                  alignment: WrapAlignment.start,
                  runAlignment: WrapAlignment.start,
                  crossAxisAlignment: WrapCrossAlignment.start,
                  children: List.generate(state.recentSearches.length, (i) {
                    return InkWell(
                      onTap: () {
                        context.read<SearchBloc>().add(
                            SearchEvent.selectRecent(state.recentSearches[i]));
                      },
                      child: Container(
                        padding: EdgeInsets.fromLTRB(12, 10, 12, 9),
                        decoration: BoxDecoration(
                            color: AppColors.neutral100,
                            border: Border.all(
                                color: AppColors.primary100, width: 1),
                            borderRadius: BorderRadius.circular(99),
                            boxShadow: [
                              BoxShadow(
                                offset: Offset(0, 1),
                                blurRadius: 3,
                                spreadRadius: 0,
                                color: Color.fromRGBO(0, 0, 0, 0.08),
                              )
                            ]),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CustomImage(
                              imageUrl: '',
                              height: 20,
                              width: 22,
                              fallbackImage: 'assets/new/icons/search.png',
                            ),
                            SizedBox(width: 8),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 4),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                    maxWidth:
                                        MediaQuery.of(context).size.width -
                                            120),
                                child: CustomText(
                                  state.recentSearches[i],
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primary500,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: BlocProvider.value(
                value: getIt<HomeBloc>(),
                child: MostBoughtSection(),
              ),
            ),
          ],
        ),
      );
    }

    if (!state.isLoading && state.products.isEmpty && state.query.isNotEmpty) {
      return NoProductsMessage(
        query: state.query,
      );
    }

    // return SizedBox();
    return SearchResult(
      state: state,
    );
  }
}

class SearchResult extends StatelessWidget {
  const SearchResult({super.key, required this.state});

  final SearchState state;

  final bool _showCategories = false;
  final bool _showSubcategories = false;

  @override
  Widget build(BuildContext context) {
    return FloatingCartWrapper(
      // excludeFloatingCart: isCartScreen,
      excludeOrderTile: true,
      bottomPadding: 0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(AppDimensions.screenHzPadding,
                12, AppDimensions.screenHzPadding, 8),
            child: CustomText(
              "Search results",
              fontSize: 20,
              fontWeight: FontWeight.w800,
              color: AppColors.primary700,
            ),
          ),

          // Categories section (if any)
          if (_showCategories && state.categories.isNotEmpty)
            // _buildCategoriesSection(),

            // Debug text to show category count
            if (_showCategories && state.categories.isNotEmpty)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Found ${state.categories.length} categories',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ),

          // Subcategories section (if any)
          if (_showSubcategories &&
              state.subcategories.isNotEmpty &&
              state.categories.isNotEmpty)
            const SizedBox(height: 16),

          if (_showSubcategories && state.subcategories.isNotEmpty)
            // _buildSubcategoriesSection(),

            // Debug text to show subcategory count
            if (_showSubcategories && state.subcategories.isNotEmpty)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Found ${state.categories.length} subcategories',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ),

          // Products section
          if (state.products.isNotEmpty)
            Expanded(
                child: ProductsSection(
              title: 'Products',
              onSeeAllTap: () {}, // Already showing all products
              preloadData: false, // We'll provide our own data
              showSeeAll: false,
              useGridView: true,
              imageHeight: 80,
              gridCrossAxisCount: 3,
              gridChildAspectRatio: 0.005,
              scrollController: SearchBloc.scrollController,
              // // Pass search results as external products
              bottomPadding: 100,
              externalProducts: state.products
                  .map((json) => ProductModel.fromJson(json))
                  .toList(),
              // // Pass load more callback for pagination only if enabled
              onLoadMore: () {
                if (SearchBloc.enableSearchPagination) {
                  if (context.mounted) {
                    context.read<SearchBloc>().add(SearchEvent.loadMore());
                  }
                }
              },
            )),

          // No results message
          if (state.products.isEmpty &&
              state.categories.isEmpty &&
              state.subcategories.isEmpty &&
              !state.isLoading &&
              state.query.isNotEmpty)
            NoProductsMessage(
              query: state.query,
            ),
        ],
      ),
    );
  }
}

class NoProductsMessage extends StatelessWidget {
  const NoProductsMessage({super.key, required this.query});

  final String query;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.screenHzPadding, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              'assets/new/images/groceries_bag.png',
              width: 150,
              height: 150,
            ),
            const SizedBox(height: 16),
            CustomText(
              'Oops! Couldn’t find products for "$query"',
              fontSize: 20,
              fontWeight: FontWeight.w800,
              color: AppColors.primary700,
              textAlign: TextAlign.center,
              maxLines: 3,
            ),
            const SizedBox(height: 6),
            CustomText(
              'Raise a request and we’ll make it available for you on the app very soon',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.neutral500,
              textAlign: TextAlign.center,
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }
}

class DottedLine extends StatelessWidget {
  final double height;
  final Color color;
  final double dashWidth;
  final double dashSpace;

  const DottedLine({
    super.key,
    this.height = 1,
    this.color = Colors.black,
    this.dashWidth = 5,
    this.dashSpace = 5,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _DottedLinePainter(
        height: height,
        color: color,
        dashWidth: dashWidth,
        dashSpace: dashSpace,
      ),
    );
  }
}

class _DottedLinePainter extends CustomPainter {
  final double height;
  final Color color;
  final double dashWidth;
  final double dashSpace;

  _DottedLinePainter({
    required this.height,
    required this.color,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = height;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(_DottedLinePainter oldDelegate) {
    return oldDelegate.height != height ||
        oldDelegate.color != color ||
        oldDelegate.dashWidth != dashWidth ||
        oldDelegate.dashSpace != dashSpace;
  }
}
