import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_text.dart';
import '../../../../widgets/custom_textfield.dart';
import '../../bloc/login_bloc/login_bloc.dart';

class LoginBottomSheet extends StatelessWidget {
  const LoginBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 70, right: 20),
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(99),
                        gradient: LinearGradient(
                          colors: [
                            Color.fromARGB(255, 155, 155, 155),
                            Color(0xFF696969),
                          ],
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                        )),
                    child: Center(
                      child: CustomText(
                        'Skip',
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.neutral100,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      bottomSheet: AnimatedPadding(
        padding: EdgeInsets.only(bottom: 0
            // MediaQuery.of(context).viewInsets.bottom
            ),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.neutral100,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 10, 20, 12),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: CustomText(
                        'Login or Sign Up to get started',
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: AppColors.primary700,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(thickness: 0.5, color: AppColors.neutral150, height: 0.5),
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 16, 20, 12),
                child: BlocBuilder<LoginBloc, LoginState>(
                  builder: (context, state) {
                    bool isValid = state.map(
                      initial: (value) => value.isLoginValid,
                      otp: (value) => value.isOTPValid,
                    );
                    return Column(
                      children: [
                        CustomField(state: state),
                        const SizedBox(height: 30),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              if (state.isLoading) return;

                              state.map(initial: (_) {
                                // _sendOtp
                                context
                                    .read<LoginBloc>()
                                    .add(LoginEvent.submitLogin());
                              }, otp: (_) {
                                // _verifyOtp
                                context
                                    .read<LoginBloc>()
                                    .add(LoginEvent.submitOTP());
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isValid
                                  ? AppColors.primary500
                                  : AppColors.primary100,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 0,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: state.isLoading
                                ? SizedBox(
                                    width: 28,
                                    height: 28,
                                    child: CircularProgressIndicator(
                                      color: AppColors.neutral100,
                                    ),
                                  )
                                : CustomText(
                                    state.map(
                                        initial: (_) => "Get OTP",
                                        otp: (value) => 'Verify & Proceed'),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w700,
                                    color: isValid
                                        ? AppColors.neutral100
                                        : AppColors.primary300,
                                  ),
                          ),
                        ),
                        state.maybeMap(
                          otp: (otpState) {
                            return Center(
                              child: Padding(
                                padding: const EdgeInsets.only(top: 16),
                                child: TextButton(
                                  onPressed: () {
                                    state.mapOrNull(
                                      otp: (value) {
                                        if (value.canResend) {
                                          context
                                              .read<LoginBloc>()
                                              .add(LoginEvent.resendOTP());
                                        }
                                      },
                                    );
                                  },
                                  child: CustomText(
                                    state.mapOrNull(
                                            otp: (value) => value.canResend
                                                ? 'Resend OTP'
                                                : 'Resend OTP in ${value.resendSeconds} seconds') ??
                                        '',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: state.maybeMap(
                                          otp: (value) => value.canResend
                                              ? AppColors.primary
                                              : AppColors.neutral300,
                                          orElse: () => AppColors.neutral300),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                          orElse: () => SizedBox(),
                        ),
                        const SizedBox(height: 45),
                        Text.rich(
                          TextSpan(
                            text: "By signing in I agree to the ",
                            children: [
                              TextSpan(
                                text: "Terms of Service",
                                style: TextStyle(color: AppColors.secondary500),
                              ),
                              TextSpan(text: " & "),
                              TextSpan(
                                text: "Privacy Policy",
                                style: TextStyle(color: AppColors.secondary500),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: AppColors.neutral500,
                            height: 1.2,
                            fontFamily: 'Mukta',
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomField extends StatelessWidget {
  const CustomField({super.key, required this.state});

  final LoginState state;

  @override
  Widget build(BuildContext context) {
    return state.map(initial: (_) {
      return !kIsWeb
          ? GestureDetector(
              onTap: () async {
                try {
                  FocusManager.instance.primaryFocus?.unfocus();
                  await Future<void>.delayed(const Duration(milliseconds: 100));
                  final phoneNumber = await SmsAutoFill().hint;
                  if (phoneNumber != null && phoneNumber.isNotEmpty) {
                    final digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
                    final last10Digits = digits.length > 10
                        ? digits.substring(digits.length - 10)
                        : digits;
                    // setState(() {
                    LoginBloc.mobileNumberManager?.text = last10Digits;
                    // });
                  }
                } catch (e) {
                  debugPrint('Error getting phone hint: $e');
                }
              },
              child: AbsorbPointer(
                absorbing: false,
                child: RegularPhoneField(),
              ),
            )
          : RegularPhoneField();
    }, otp: (_) {
      return !kIsWeb
          ? OTPField(
              autoFill: true,
            )
          : OTPField();
    });
  }
}

class OTPField extends StatelessWidget {
  const OTPField({super.key, this.autoFill = false});
  final bool autoFill;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: LoginBloc.otpManager?.errorText ?? ValueNotifier(''),
        builder: (context, error, _) {
          return CustomTextField(
            controller: LoginBloc.otpManager?.controller,
            focusNode: LoginBloc.otpManager?.focusNode,
            keyboardType: TextInputType.number,
            maxLength: 6,
            hintText: "Enter OTP",
            style: TextStyle(
              fontFamily: 'Mukta',
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.neutral800,
            ),
            textInputAction: TextInputAction.done,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 18,
              ),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              disabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              hintStyle: TextStyle(
                color: AppColors.primary200,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              errorText: error.isNotEmpty ? error : null,
            ),
            onChanged: autoFill
                ? (value) {
                    if (value.length == 6) {
                      Future<void>.delayed(const Duration(milliseconds: 500),
                          () {
                        // ignore: use_build_context_synchronously
                        context.read<LoginBloc>().add(LoginEvent.submitOTP());
                      });
                    }
                  }
                : null,
          );
        });
  }
}

class RegularPhoneField extends StatelessWidget {
  const RegularPhoneField({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable:
            LoginBloc.mobileNumberManager?.errorText ?? ValueNotifier(''),
        builder: (context, error, _) {
          return CustomTextField(
            controller: LoginBloc.mobileNumberManager?.controller,
            focusNode: LoginBloc.mobileNumberManager?.focusNode,
            keyboardType: TextInputType.phone,
            maxLength: 10,
            hintText: "Enter Phone Number",
            style: TextStyle(
              fontFamily: 'Mukta',
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.neutral800,
            ),
            textInputAction: TextInputAction.done,
            // Add autofillHints for iOS phone number suggestion
            autofillHints: const [AutofillHints.telephoneNumber],
            onChanged: (value) {
              // Auto-submit when a valid 10-digit number is entered (for iOS autofill)
              if (value.length == 10 && !kIsWeb && Platform.isIOS) {
                // Delay slightly to ensure the field is fully populated
                Future.delayed(const Duration(milliseconds: 300), () {
                  getIt<LoginBloc>().add(LoginEvent.submitLogin());
                });
              }
            },
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              disabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary700, width: 1),
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: Padding(
                padding: const EdgeInsets.only(left: 16.0, right: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomText(
                      '+91',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.neutral800,
                    ),
                    const SizedBox(width: 8),
                    Image.asset(
                      'assets/new/icons/chevron-down.png',
                      height: 20,
                      width: 20,
                    )
                  ],
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 18,
              ),
              hintStyle: TextStyle(
                color: AppColors.primary200,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              errorText: error.isNotEmpty ? error : null,
            ),
          );
        });
  }
}
