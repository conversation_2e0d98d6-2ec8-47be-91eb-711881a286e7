import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../domain/entities/product_entity.dart';

part 'free_product_event.freezed.dart';

@freezed
class FreeProductEvent with _$FreeProductEvent {
  /// Event triggered when checking for free product offers
  /// Should be called when:
  /// - User visits the cart page
  /// - Cart contents are updated (items added/removed/quantity changed)
  const factory FreeProductEvent.checkOffer({
    required double cartAmount,
  }) = CheckOfferEvent;

  /// Event triggered to fetch and display free products
  /// Called when CheckOfferEvent returns valid productId and variantId
  const factory FreeProductEvent.getProducts({
    required String productId,
    required String variantId,
  }) = GetProductsEvent;

  /// Event triggered to add a free product to the separate free products cart
  const factory FreeProductEvent.addFreeProduct({
    required ProductEntity product,
  }) = AddFreeProductEvent;

  /// Event triggered to remove a free product from the separate free products cart
  const factory FreeProductEvent.removeFreeProduct({
    required String productId,
    required String skuId,
  }) = RemoveFreeProductEvent;

  /// Event triggered to clear all free products from the cart
  const factory FreeProductEvent.clearFreeProducts() = ClearFreeProductsEvent;
}
