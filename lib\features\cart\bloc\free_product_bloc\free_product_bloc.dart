import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/dependency_injection/di_container.dart';
import '../../../../data/mappers/product_mapper.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../search/services/typesense_service.dart';
import 'free_product_event.dart';
import 'free_product_state.dart';

class FreeProductBloc extends Bloc<FreeProductEvent, FreeProductState> {
  final TypesenseService _typesenseService;

  FreeProductBloc({
    TypesenseService? typesenseService,
  })  : _typesenseService = typesenseService ?? getIt<TypesenseService>(),
        super(FreeProductState.initial()) {
    on<CheckOfferEvent>(_onCheckOffer);
    on<GetProductsEvent>(_onGetProducts);
    on<AddFreeProductEvent>(_onAddFreeProduct);
    on<RemoveFreeProductEvent>(_onRemoveFreeProduct);
    on<ClearFreeProductsEvent>(_onClearFreeProducts);
  }

  /// Handle checking for free product offers based on cart amount
  Future<void> _onCheckOffer(
    CheckOfferEvent event,
    Emitter<FreeProductState> emit,
  ) async {
    try {
      emit(FreeProductState.checkingOffer());

      final result = await _typesenseService.getFreeProductsByCartAmount(
        cartAmount: event.cartAmount,
        pageSize: 1,
      );

      if (result != null) {
        final productId = result['productId'] as String?;
        final variantId = result['variantId'] as String?;

        if (productId != null && variantId != null) {
          // Emit offer found state
          emit(FreeProductState.offerFound(
            productId: productId,
            variantId: variantId,
          ));

          // Automatically trigger GetProductsEvent
          add(FreeProductEvent.getProducts(
            productId: productId,
            variantId: variantId,
          ));
        } else {
          emit(FreeProductState.noOffer());
        }
      } else {
        emit(FreeProductState.noOffer());
      }
    } catch (e) {
      emit(FreeProductState.error('Failed to check for free product offers'));
    }
  }

  /// Handle fetching and transforming free products
  Future<void> _onGetProducts(
    GetProductsEvent event,
    Emitter<FreeProductState> emit,
  ) async {
    try {
      emit(FreeProductState.loading());

      final productsJson = await _typesenseService.searchProducts(
        productId: event.productId,
        variantId: event.variantId,
        pageSize: 1,
      );

      if (productsJson.isNotEmpty) {
        // Transform products to have price = 0
        final freeProducts = ProductMapper.fromJsonList(productsJson)
            .map((product) => product.copyWith(
                  price: 0.0,
                  originalPrice:
                      product.price, // Keep original price for reference
                  discountLabel: 'FREE',
                ))
            .toList();

        emit(FreeProductState.productsLoaded(
          products: freeProducts,
          productId: event.productId,
          variantId: event.variantId,
        ));
      } else {
        emit(FreeProductState.error('Free product not found'));
      }
    } catch (e) {
      emit(FreeProductState.error('Failed to load free products'));
    }
  }

  /// Handle adding a free product to the separate free products cart
  Future<void> _onAddFreeProduct(
    AddFreeProductEvent event,
    Emitter<FreeProductState> emit,
  ) async {
    try {
      final currentSelectedProducts =
          List<ProductEntity>.from(state.selectedFreeProducts);

      // Check if product is already in the free products cart
      final existingIndex = currentSelectedProducts.indexWhere(
        (product) =>
            product.id == event.product.id &&
            product.skuID == event.product.skuID,
      );

      if (existingIndex == -1) {
        // Add the product to the free products cart
        currentSelectedProducts.add(event.product);

        emit(state.copyWith(selectedFreeProducts: currentSelectedProducts));
      } else {
        emit(state.copyWith(error: 'Free product already in cart'));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Failed to add free product'));
    }
  }

  /// Handle removing a free product from the separate free products cart
  Future<void> _onRemoveFreeProduct(
    RemoveFreeProductEvent event,
    Emitter<FreeProductState> emit,
  ) async {
    try {
      final currentSelectedProducts =
          List<ProductEntity>.from(state.selectedFreeProducts);

      // Remove the product from the free products cart
      currentSelectedProducts.removeWhere(
        (product) =>
            product.id == event.productId && product.skuID == event.skuId,
      );

      emit(state.copyWith(selectedFreeProducts: currentSelectedProducts));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to remove free product'));
    }
  }

  /// Handle clearing all free products from the cart
  Future<void> _onClearFreeProducts(
    ClearFreeProductsEvent event,
    Emitter<FreeProductState> emit,
  ) async {
    try {
      emit(state.copyWith(selectedFreeProducts: []));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to clear free products'));
    }
  }
}
