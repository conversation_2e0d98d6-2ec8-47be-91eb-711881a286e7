import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/color_utils.dart';

import 'package:rozana/widgets/viewall_category_title.dart';

import '../../../../core/dependency_injection/di_container.dart';
import '../../../../data/services/data_loading_manager.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../widgets/lazy_loading_widget.dart';
import '../../bloc/categories_bloc.dart';
import 'category_skeleton_loader.dart';
import 'categoy_card.dart';

class SubCategoriesSection extends StatefulWidget {
  final CategoryEntity? currentSelectedCategory;
  final CategoryEntity? parentCategory;
  final VoidCallback? onSeeAllTap;
  final Function(CategoryEntity?)? onSubcategorySelected;
  final bool showTitleBar;

  final bool preloadData;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showAsRow;
  final String? level;

  final bool highliteSelectd;
  final Map<String, dynamic>? dynamicQuery;

  final String? highlightPrimaryColor;
  final String? highlightSecondaryColor;
  final String? dividerColor;

  const SubCategoriesSection({
    super.key,
    this.onSeeAllTap,
    this.onSubcategorySelected,

    // DataLoadingManager is now accessed directly
    this.preloadData = true,
    this.useGridView = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.72,
    this.showAsRow = false,
    this.parentCategory,
    this.showTitleBar = true,
    this.level,
    this.currentSelectedCategory,
    this.highliteSelectd = false,
    this.dynamicQuery,
    this.highlightPrimaryColor,
    this.highlightSecondaryColor,
    this.dividerColor,
  });

  @override
  State<SubCategoriesSection> createState() => _SubCategoriesSectionState();
}

class _SubCategoriesSectionState extends State<SubCategoriesSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<CategoryEntity> _subCategories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.preloadData) {
      _loadCategories();
    }
  }

  @override
  void didUpdateWidget(covariant SubCategoriesSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.parentCategory?.collectionId !=
        oldWidget.parentCategory?.collectionId) {
      _loadCategories();
    }
  }

  Future<void> _loadCategories() async {
    if ((widget.parentCategory == null) ||
        (!CategoriesBloc.loadedSubcategories
            .containsKey(widget.parentCategory?.name))) {
      if (_isLoading) return;

      setState(() {
        _isLoading = true;
      });

      try {
        final categories = widget.parentCategory == null
            ? await _dataManager.loadCategories(pageSize: 20)
            : await _dataManager.loadSubCategories(
                category: widget.parentCategory,
                pageSize: 20,
                level: widget.level,
                dynamicQuery: widget.dynamicQuery);

        setState(() {
          _subCategories.clear();
          _subCategories.addAll(_processCategories(categories));
          _isLoading = false;
          CategoriesBloc
                  .loadedSubcategories[widget.parentCategory?.name ?? ''] =
              categories;
        });
        if (_subCategories.isNotEmpty && widget.onSubcategorySelected != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            widget.onSubcategorySelected!(_subCategories.first);
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } else {
      final cachedCategories = CategoriesBloc
              .loadedSubcategories[widget.parentCategory?.name ?? ''] ??
          [];
      _subCategories.clear();
      _subCategories.addAll(_processCategories(cachedCategories));

      if (_subCategories.isNotEmpty && widget.onSubcategorySelected != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onSubcategorySelected!(_subCategories.first);
        });
      }
    }
  }

  List<CategoryEntity> _processCategories(List<CategoryEntity> categories) {
    if (widget.level == 'sub_sub_category' && categories.length >= 2) {
      final allCategory = CategoryEntity(
        id: 'all_${widget.parentCategory?.collectionId ?? ''}',
        name: 'All',
        collectionId: widget.parentCategory?.collectionId ?? '',
        parentID: widget.parentCategory?.parentID,
        level: 'all_sub_sub_category',
        count: categories.fold<int>(0, (sum, cat) => sum + (cat.count ?? 0)),
        imageUrl: widget.parentCategory?.imageUrl,
      );
      return [allCategory, ...categories];
    }
    return categories;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitleBar)
          Padding(
            padding:
                const EdgeInsets.only(left: 16, top: 0, right: 8, bottom: 8),
            child: ViewAllCategoryTitle(
              title: widget.parentCategory?.name ?? '',
              onTap: () {
                HapticFeedback.lightImpact();
                widget.onSeeAllTap?.call();
              },
              showViewAll: false,
            ),
          ),
        _isLoading
            ? Center(
                child: CategorySkeletonLoader(
                  useGridView: widget.useGridView,
                  showAsRow: widget.showAsRow,
                  gridCrossAxisCount: widget.gridCrossAxisCount,
                  gridChildAspectRatio: widget.gridChildAspectRatio,
                ),
              )
            : widget.showAsRow
                ? _buildCategoryRow()
                : widget.useGridView
                    ? _buildCategoryGrid()
                    : _buildDefaultCategoryGrid(),
      ],
    );
  }

  Widget _buildDefaultCategoryGrid() {
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding:
          const EdgeInsets.symmetric(horizontal: AppDimensions.screenHzPadding),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridCrossAxisCount,
        childAspectRatio: widget.gridChildAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 16,
      ),
      itemCount: _subCategories.length,
      itemBuilder: (context, index) {
        CategoryEntity subCategory = _subCategories[index];
        return CategoryCard(
          category: widget.parentCategory,
          subCategory: subCategory,
          radius: 12,
          fontSize: 14,
          textColor: AppColors.neutral500,
        );
      },
    );
  }

  Widget _buildCategoryGrid() {
    return GridLazyLoadingWidget<CategoryEntity>(
      items: _subCategories,
      isLoading: _isLoading,
      hasMoreData: false,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridCrossAxisCount,
        childAspectRatio: widget.gridChildAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, category, index) {
        CategoryEntity subCategory = _subCategories[index];

        return CategoryCard(
          category: widget.parentCategory,
          subCategory: subCategory,
        );
      },
    );
  }

  Widget _buildCategoryRow() {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: _subCategories.length,
      itemBuilder: (context, index) {
        CategoryEntity subCategory = _subCategories[index];
        bool isSelected =
            (widget.currentSelectedCategory?.name == subCategory.name) &&
                (widget.highliteSelectd);
        return IntrinsicHeight(
          child: Stack(
            children: [
              Row(
                children: [
                  Spacer(),
                  Container(
                    constraints: BoxConstraints(maxHeight: 100),
                    decoration: BoxDecoration(
                      border: Border(
                          right: BorderSide(
                        width: 3.2,
                        color: isSelected
                            ? (ColorUtils.hexToColor(
                                    widget.dividerColor ?? '') ??
                                AppColors.primary500)
                            : Colors.transparent,
                      )),
                      gradient: isSelected
                          ? LinearGradient(
                              colors: [
                                (ColorUtils.hexToColor(
                                            widget.highlightPrimaryColor ??
                                                '') ??
                                        AppColors.primary200)
                                    .withValues(alpha: 0.1),
                                ColorUtils.hexToColor(
                                        widget.highlightSecondaryColor ?? '') ??
                                    AppColors.neutral100,
                              ],
                              begin: Alignment.centerRight,
                              end: Alignment.centerLeft,
                            )
                          : null,
                    ),
                    child: CategoryCard(
                      color: Colors.transparent,
                      width: MediaQuery.of(context).size.width * 0.16,
                      // height: 100,
                      imagePadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      category: widget.parentCategory,
                      subCategory: subCategory,
                      textPadding: EdgeInsets.zero,
                      fontSize: 12,
                      textColor: AppColors.primary500,
                      fontWeight:
                          isSelected ? FontWeight.w700 : FontWeight.w500,
                      onTap: () {
                        widget.onSubcategorySelected?.call(subCategory);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
      separatorBuilder: (context, index) => SizedBox(height: 8),
    );
  }
}
