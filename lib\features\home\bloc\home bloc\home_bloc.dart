export 'home_event.dart';
export 'home_state.dart';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/services/appflyer_services/app_flyer_deeplink.dart';
import 'package:rozana/domain/usecases/get_categories_usecase.dart';
import '../../../../app/bloc/app_bloc.dart';
import '../../../../core/dependency_injection/di_container.dart';
import '../../../../core/services/appflyer_services/app_flyers_services.dart';
import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../domain/usecases/get_banners_usecase.dart';
import '../../../../domain/usecases/get_order_history_usecase.dart';
import '../../../../domain/usecases/get_products_usecase.dart';
import '../../../../domain/usecases/get_sub_categories_usecase.dart';

import 'home_event.dart';
import 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetSubCategoriesUseCase _getSubCategoriesUseCase;
  final GetProductsUseCase _getProductsUseCase;
  final GetBannersUseCase _getBannersUseCase;
  final GetCategoriesUseCase _getcategoriesUseCase;
  final GetOrderHistoryUseCase _getOrderHistoryUseCase;

  static ScrollController? scrollController;

  static List<GlobalKey>? categoryKeys;

  HomeBloc({
    required GetSubCategoriesUseCase getSubCategoriesUseCase,
    required GetProductsUseCase getProductsUseCase,
    required GetBannersUseCase getBannersUseCase,
    required GetCategoriesUseCase categoriesUseCase,
    required GetOrderHistoryUseCase getOrderHistoryUseCase,
  })  : _getSubCategoriesUseCase = getSubCategoriesUseCase,
        _getProductsUseCase = getProductsUseCase,
        _getBannersUseCase = getBannersUseCase,
        _getcategoriesUseCase = categoriesUseCase,
        _getOrderHistoryUseCase = getOrderHistoryUseCase,
        super(const HomeState.initial()) {
    scrollController = ScrollController();
    // Initial state is now a const factory constructor
    on<InitHome>(_onInitHome);
    on<UpdateScroll>(_onUpdateScroll);
    on<LoadHomeData>(_onLoadHomeData);
    on<UpdateHomeList>(_onUpdateHomeList);
    on<LoadDeepLink>(_onDeepLinkFound);
    on<ScrollDirectionChanged>(_onBottomScrollChanged);
    on<SwitchCategory>(_onSwitchCategory);
  }

  void _onInitHome(InitHome event, Emitter<HomeState> emit) async {
    try {
      _handleDeepLink();
      scrollController?.addListener(() {
        final scrolled = (scrollController?.offset ?? 0) > 350;
        // if (scrolled != state.isScrolled) {
        add(UpdateScroll(scrolled, scrollController?.offset ?? 0));
        // }
      });
      add(HomeEvent.loadHomeData());
    } catch (_) {}
  }

  void _onDeepLinkFound(LoadDeepLink event, Emitter<HomeState> emit) async {
    try {
      emit(HomeState.deepLink(
          isScrolled: state.isScrolled, route: event.route, args: event.args));
    } catch (_) {}
  }

  void _onUpdateScroll(UpdateScroll event, Emitter<HomeState> emit) {
    emit(state.copyWith(isScrolled: event.scroll, scrollOffset: event.offset));
  }

  void _onSwitchCategory(SwitchCategory event, Emitter<HomeState> emit) {
    if (state is HomeLoaded) {
      HomeLoaded loadedState = state as HomeLoaded;
      emit(loadedState.copyWith(
          selectedCategory: event.category ??
              CategoryEntity(
                  id: 'My Deals', name: 'My Deals', collectionId: 'My Deals'),
          selectedIndex: event.index,
          banners: null));
      _getBannersUseCase
          .execute(level: event.category?.name ?? 'My Deals')
          .then((value) {
        add(HomeEvent.updateLoadedList(banners: value));
      });
    } else {
      emit(state.copyWith(
          selectedCategory: event.category ??
              CategoryEntity(
                  id: 'My Deals', name: 'My Deals', collectionId: 'My Deals'),
          selectedIndex: event.index));
    }
  }

  void _onBottomScrollChanged(
      ScrollDirectionChanged event, Emitter<HomeState> emit) {
    final currentVisibility = state.showBottomNavBar;
    if (event.direction == ScrollDirection.reverse && currentVisibility) {
      emit(state.copyWith(showBottomNavBar: false));
    } else if (event.direction == ScrollDirection.forward &&
        !currentVisibility) {
      emit(state.copyWith(showBottomNavBar: true));
    }
  }

  Future<void> _onLoadHomeData(
    LoadHomeData event, // LoadHomeData is now a class, not just a type
    Emitter<HomeState> emit,
  ) async {
    try {
      // Start all futures without awaiting
      final categoriesSectionFuture =
          _getcategoriesUseCase.execute(secComponent: 'category');
      final categoriesFuture =
          _getSubCategoriesUseCase.execute(level: 'category', pageSize: 100);
      final subCategoriesFuture =
          _getcategoriesUseCase.execute(secComponent: 'BTS');

      final mostBoughtFuture = _getProductsUseCase.execute(
          query: '',
          sectionType: 'bsp',
          pageSize: 12,
          dynamic: false); // Using sectionType 'mbi' for Most Bought Items
      final bannersFuture =
          _getBannersUseCase.execute(level: state.selectedCategory.name);

      // Only call getPreviousOrders if user is authenticated to prevent token refresh errors
      Future<List<dynamic>>? ordersFuture;
      if (getIt<AppBloc>().isAuthenticated) {
        ordersFuture = _getOrderHistoryUseCase.getPreviousOrders();
      }

      // Keep current progressive data
      List<CategoryEntity>? categorySection;
      List<CategoryEntity>? categories;
      List<CategoryEntity>? subCategories;
      List<ProductEntity>? mostBought;
      List<BannerEntity>? banners;

      ordersFuture?.then((value) {
        add(HomeEvent.updateLoadedList(
            categorySections: categorySection ?? []));
      });

      categoriesSectionFuture.then((value) {
        categorySection = value;
        add(HomeEvent.updateLoadedList(
            categorySections: categorySection ?? []));
      });

      categoriesFuture.then((value) {
        categories = value;

        add(HomeEvent.updateLoadedList(categories: categories ?? []));
      });
      subCategoriesFuture.then((value) {
        subCategories = value;

        add(HomeEvent.updateLoadedList(subCategories: subCategories ?? []));
      });

      mostBoughtFuture.then((value) {
        mostBought = value;
        add(HomeEvent.updateLoadedList(mostBought: mostBought ?? []));
      });

      bannersFuture.then((value) {
        banners = value;
        add(HomeEvent.updateLoadedList(banners: banners ?? []));
      });
    } catch (e) {
      emit(HomeState.error(
          message: 'Failed to load home data: $e',
          isScrolled: state.isScrolled)); // Emit const factory constructor
    }
  }

  void _onUpdateHomeList(UpdateHomeList event, Emitter<HomeState> emit) {
    if (state is HomeLoaded) {
      HomeLoaded loadedState = state as HomeLoaded;
      emit(loadedState.copyWith(
        categorySections:
            event.categorySections ?? loadedState.categorySections,
        categories: event.categories ?? loadedState.categories,
        subCategories: event.subCategories ?? loadedState.subCategories,
        mostBought: event.mostBought ?? loadedState.mostBought,
        banners: event.banners ?? loadedState.banners,
      ));
    } else {
      emit(HomeState.loaded(
          selectedCategory: CategoryEntity(
              id: 'My Deals', name: 'My Deals', collectionId: 'My Deals'),
          selectedIndex: 0,
          categorySections: event.categorySections,
          categories: event.categories,
          subCategories: event.subCategories,
          mostBought: event.mostBought,
          banners: event.banners,
          isScrolled: state.isScrolled,
          scrollOffset: state.scrollOffset));
    }
  }

  void _handleDeepLink() {
    String? pendingLink = AppsFlyerServices.pendingLink;
    if (pendingLink != null) {
      add(HomeEvent.deepLinkFound(
          pendingLink, AppsFlyerServices.pendingLinkArguments ?? {}));
      // Clear the pending link after processing to prevent re-triggering
      AppsFlyerServices.pendingLink = null;
      AppsFlyerServices.pendingLinkArguments = null;
    } else {
      AppFlyerDeeplink.onDeepLink((route, args) {
        add(HomeEvent.deepLinkFound(route, args));
      });
    }
  }

  @override
  Future<void> close() {
    scrollController?.dispose();
    return super.close();
  }
}

