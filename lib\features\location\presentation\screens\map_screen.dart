import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/network/google_api_client.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/features/location/bloc/location_permission_bloc/location_permission_bloc.dart';
import 'package:rozana/features/location/bloc/location_permission_bloc/location_permission_event.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/logger.dart';
import '../../../../widgets/custom_button.dart';
import '../../services/adress_services.dart';
import '../../services/google_places_service.dart';
import 'package:flutter/foundation.dart'; // <-- Add this import

class MapScreen extends StatefulWidget {
  final AddressModel? address;
  final Function(double latitude, double longitude)? onLocationSelected;
  final bool fromCart;
  const MapScreen({
    super.key,
    this.address,
    this.fromCart = false,
    this.onLocationSelected,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final AddressService _addressService = AddressService();
  final GooglePlacesService _googlePlacesService = GooglePlacesService();
  final GoogleApiClient _googleApiClient = GoogleApiClient();
  GoogleMapController? _mapController;
  double? _currentLatitude;
  double? _currentLongitude;
  bool _isLoading = false;
  bool _isAddressReady = false;
  bool _hasLocationError = false;
  String _errorMessage = '';
  String _currentAddress = 'Loading address...';
  final token = Uuid().v4();
  List<dynamic>? locations;
  String _mapStyle = '';
  Set<Marker> _markers = {};

  // Add search controller
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
    rootBundle.loadString('assets/map/snazzy_style.json').then((style) {
      _mapStyle = style;
    });
  }

  void _initializeLocation() async {
    try {
      setState(() {
        _hasLocationError = false;
        _errorMessage = '';
      });

      getIt<LocationPermissionBloc>()
          .add(LocationPermissionEvent.checkPermissions());

      if (widget.address != null &&
          widget.address!.latitude != null &&
          widget.address!.longitude != null) {
        await _setLocationAndFetchAddress(
          (widget.address!.latitude as double),
          (widget.address!.longitude as double),
        );
      } else {
        final position = await _addressService.getCurrentPosition();
        if (position != null) {
          await _setLocationAndFetchAddress(
              position.latitude, position.longitude);
        } else {
          // Use Delhi coordinates as fallback
          await _setLocationAndFetchAddress(28.6139, 77.2090);
        }
      }
    } catch (e) {
      setState(() {
        _hasLocationError = true;
        _errorMessage = 'Failed to load location. Please try again.';
      });
    }
  }

  Future<void> _setLocationAndFetchAddress(
      double latitude, double longitude) async {
    setState(() {
      _currentLatitude = latitude;
      _currentLongitude = longitude;
    });
    _updateMarker();
    await _getAddressFromCoordinates(latitude, longitude);
  }

  void _updateMarker() {
    if (_currentLatitude != null && _currentLongitude != null) {
      setState(() {
        _markers = {
          Marker(
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueAzure),
            markerId: const MarkerId('selected_location'),
            position: LatLng(_currentLatitude!, _currentLongitude!),
            anchor:
                const Offset(0.5, 0.5), // Center the marker on the coordinates
            infoWindow: const InfoWindow(
              title: 'Selected Location',
            ),
          ),
        };
      });
    }
  }

  Future<void> _getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      // Use the enhanced address method to get building names
      final enhancedAddress = await _addressService.getEnhancedAddress(
        latitude,
        longitude,
      );
      if (kIsWeb) {
        final address = await _googleApiClient.getAddressFromLatLngForWeb(
            latitude, longitude);
        setState(() {
          _currentAddress = address ??
              'Lat: ${latitude.toStringAsFixed(5)}, Lng: ${longitude.toStringAsFixed(5)}';
        });
        return;
      }
      setState(() {
        _currentAddress = enhancedAddress;
      });
    } catch (e) {
      setState(() {
        _currentAddress = 'Failed to get address';
      });
    }
  }

  void _onCameraMove(CameraPosition position) {
    // Update coordinates as map moves
    _currentLatitude = position.target.latitude;
    _currentLongitude = position.target.longitude;
    _updateMarker();
  }

  void _onCameraIdle() {
    if (_currentLatitude != null && _currentLongitude != null) {
      _getAddressFromCoordinates(_currentLatitude!, _currentLongitude!);
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final position = await _addressService.getCurrentPosition();

      if (position != null) {
        await _setLocationAndFetchAddress(
            position.latitude, position.longitude);
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(position.latitude, position.longitude),
              zoom: 20,
            ),
          ),
        );
      } else {
        _showSnackBar('Failed to get current location');
      }
    } catch (e) {
      _showSnackBar('Failed to get current location');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectLocation() async {
    if (_currentLatitude == null || _currentLongitude == null) return;

    setState(() {
      _isAddressReady = true; // Indicate process is in progress
    });

    try {
      if (widget.onLocationSelected != null) {
        widget.onLocationSelected!(_currentLatitude!, _currentLongitude!);
      }

      String? enhancedAddress;
      List<dynamic>? placemarks;
      AddressModel? addressModel;

      if (kIsWeb) {
        enhancedAddress = await _googleApiClient.getAddressFromLatLngForWeb(
          _currentLatitude!,
          _currentLongitude!,
        );

        placemarks = await _googleApiClient.getPlacemarkFromCoordinatesForWeb(
          latitude: _currentLatitude!,
          longitude: _currentLongitude!,
        );

        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          addressModel = AddressModel(
            id: widget.address?.id,
            fullAddress: enhancedAddress ?? '',
            addressLine1: placemark['street'] ?? '',
            city: placemark['locality'] ?? '',
            state: placemark['administrativeArea'] ?? '',
            pincode: placemark['postalCode'] ?? '',
            latitude: _currentLatitude!,
            longitude: _currentLongitude!,
            addressType: widget.address?.addressType ?? 'home',
            isDefault: widget.address?.isDefault ?? false,
            name: widget.address?.name,
            phone: widget.address?.phone,
          );
        }
      } else {
        enhancedAddress = await _addressService.getEnhancedAddress(
          _currentLatitude!,
          _currentLongitude!,
        );

        placemarks = await _addressService.getAddressFromCoordinates(
          _currentLatitude!,
          _currentLongitude!,
        );

        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          addressModel = AddressModel(
            id: widget.address?.id,
            fullAddress: enhancedAddress,
            addressLine1: placemark.street ?? '',
            city: placemark.locality ?? '',
            state: placemark.administrativeArea ?? '',
            pincode: placemark.postalCode ?? '',
            latitude: _currentLatitude!,
            longitude: _currentLongitude!,
            addressType: widget.address?.addressType ?? 'home',
            isDefault: widget.address?.isDefault ?? false,
            name: widget.address?.name,
            phone: widget.address?.phone,
          );
        }
      }

      setState(() {
        _currentAddress = enhancedAddress ?? '';
      });

      if (!mounted) return;

      if (!getIt<AppBloc>().isAuthenticated) {
        getIt<LocationBloc>().add(
          LocationEvent.refreshLocation(
            tempAddress: addressModel?.copyWith(addressType: 'current'),
          ),
        );
        context.pop();
        return;
      }

      if (widget.fromCart) {
        final result = await context.push<AddressModel>(
          RouteNames.editAddress,
          extra: {
            'address': addressModel,
            'fromCart': widget.fromCart,
          },
        );
        context.pop(result);
      } else {
        context.pushReplacement(
          RouteNames.editAddress,
          extra: {
            'address': addressModel,
            'fromCart': widget.fromCart,
          },
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAddressReady = false; // Always reset when done
        });
      }
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  Future<void> _searchAndMoveToLocation(String query, {String? placeId}) async {
    if (query.trim().isEmpty) return;
    setState(() {
      _isSearching = true;
    });
    try {
      // If we have a placeId, use Google Places API for accurate location
      if (placeId != null) {
        final placeDetails =
            await _googlePlacesService.getPlaceDetailsById(placeId);
        if (placeDetails != null &&
            placeDetails['geometry'] != null &&
            placeDetails['geometry']['location'] != null) {
          final location = placeDetails['geometry']['location'];
          final latitude = location['lat'] as double;
          final longitude = location['lng'] as double;

          // Get enhanced address with building name
          final enhancedAddress =
              await _addressService.getEnhancedAddress(latitude, longitude);

          setState(() {
            _currentLatitude = latitude;
            _currentLongitude = longitude;
            _currentAddress = enhancedAddress;
          });
          _updateMarker();

          _mapController?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(_currentLatitude!, _currentLongitude!),
                zoom: 20,
              ),
            ),
          );
          return;
        }
      }

      // Fallback to geocoding if place_id approach fails
      final locations = await _addressService.searchAddresses(query);
      if (locations != null && locations.isNotEmpty) {
        final location = locations.first;
        // Get enhanced address with building name
        final enhancedAddress = await _addressService.getEnhancedAddress(
            location.latitude, location.longitude);

        setState(() {
          _currentLatitude = location.latitude;
          _currentLongitude = location.longitude;
          _currentAddress = enhancedAddress;
        });
        _updateMarker();

        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(_currentLatitude!, _currentLongitude!),
              zoom: 20,
            ),
          ),
        );
      } else {
        _showSnackBar('Location not found');
      }
    } catch (e) {
      _showSnackBar('Failed to search location');
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  Future<void> placeSuggestion(String input) async {
    try {
      LogMessage.p('Requesting place suggestions for input: "$input"',
          subTag: 'MapScreen');

      final predictions = await _googlePlacesService.getPlaceAutocomplete(
        input: input,
        sessionToken: token,
      );

      LogMessage.p('Received predictions: ${predictions.length} results',
          subTag: 'MapScreen');

      if (mounted) {
        setState(() {
          locations = predictions;
        });
        LogMessage.p('Updated UI with ${locations?.length ?? 0} locations',
            subTag: 'MapScreen');
      }
    } catch (e) {
      LogMessage.p('Error getting place suggestions: $e', subTag: 'MapScreen');
      if (mounted) {
        _showSnackBar('Failed to load place suggestions');
      }
    }
  }

  void _onChange(String value) {
    // Only search if input has at least 2 characters
    if (value.trim().length >= 2) {
      placeSuggestion(value.trim());
    } else {
      // Clear suggestions for short input
      setState(() {
        locations = [];
      });
    }
  }

  Widget _buildErrorState(String message) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.neutral600,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: AppColors.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Location Error',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.neutral600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.neutral400,
                ),
              ),
              const SizedBox(height: 24),
              CustomButton(
                text: 'Try Again',
                onPressed: _initializeLocation,
                backgroundColor: AppColors.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    _mapController!.setMapStyle(_mapStyle);
  }

  @override
  Widget build(BuildContext context) {
    // Show error state if there's a location error
    if (_hasLocationError) {
      return _buildErrorState(_errorMessage);
    }

    // Show loading state if location is not yet available
    if (_currentLatitude == null || _currentLongitude == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Select Location'),
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.neutral600,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Loading location...',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.neutral400,
                ),
              ),
            ],
          ),
        ),
      );
    }
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.neutral600,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.neutral100,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.neutral800.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () => context.pop(),
            icon: Icon(Icons.arrow_back, color: AppColors.neutral600),
          ),
        ),
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.neutral100,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.neutral800.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            'Select Location',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral600,
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          // Google Map with modern styling
          GoogleMap(
            mapType: MapType.normal,
            indoorViewEnabled: true,
            initialCameraPosition: CameraPosition(
              target: LatLng(_currentLatitude!, _currentLongitude!),
              zoom: 20,
            ),
            buildingsEnabled: true,
            onMapCreated: _onMapCreated,
            onCameraMove: _onCameraMove,
            onCameraIdle: _onCameraIdle,
            myLocationEnabled: true, // We'll use custom location button
            myLocationButtonEnabled: false,
            zoomControlsEnabled: true, // We'll use custom zoom controls
            mapToolbarEnabled: false,
            compassEnabled: false,
            markers: _markers,
            zoomGesturesEnabled: true,
            rotateGesturesEnabled: true,
            scrollGesturesEnabled: true,
            tiltGesturesEnabled: true,
            style: '''[
              {
                "featureType": "poi",
                "elementType": "labels",
                "stylers": [{"visibility": "off"}]
              }
            ]''', // Hide POI labels for cleaner look
          ),

          // Modern Search Bar
          Positioned(
            top: MediaQuery.of(context).padding.top + 80,
            left: 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.neutral800.withValues(alpha: 0.1),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: TextField(
                onChanged: (value) => _onChange(value),
                controller: _searchController,
                textInputAction: TextInputAction.search,
                onSubmitted: (value) {
                  _searchAndMoveToLocation(value);
                  FocusScope.of(context).unfocus();
                  setState(() {
                    locations = [];
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Search for restaurants, shops, areas...',
                  hintStyle: TextStyle(
                    color: AppColors.neutral200,
                    fontSize: 14,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: AppColors.neutral400,
                    size: 20,
                  ),
                  suffixIcon: _isSearching
                      ? Padding(
                          padding: EdgeInsets.all(12),
                          child: SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.primary),
                            ),
                          ),
                        )
                      : (_searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: AppColors.neutral400,
                                size: 20,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  locations = [];
                                });
                              },
                            )
                          : null),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
              ),
            ),
          ),

          // Modern Search Results
          if (locations != null && locations!.isNotEmpty)
            Positioned(
              top: MediaQuery.of(context).padding.top + 140,
              left: 16,
              right: 16,
              child: Container(
                constraints: const BoxConstraints(maxHeight: 250),
                decoration: BoxDecoration(
                  color: AppColors.neutral100,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.neutral800.withValues(alpha: 0.1),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ListView.separated(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  shrinkWrap: true,
                  itemCount: locations!.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 1,
                    color: AppColors.neutral100,
                  ),
                  itemBuilder: (context, index) {
                    final location = locations![index];
                    return ListTile(
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.location_on,
                          color: AppColors.primary,
                          size: 16,
                        ),
                      ),
                      title: Text(
                        location['description'],
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.neutral600,
                        ),
                      ),
                      onTap: () {
                        _searchAndMoveToLocation(
                          location['description'],
                          placeId: location['place_id'] ?? location['placeId'],
                        );
                        setState(() {
                          locations = [];
                          _searchController.text = location['description'];
                          FocusScope.of(context).unfocus();
                        });
                      },
                    );
                  },
                ),
              ),
            ),

          // Floating Action Buttons
          Positioned(
            right: 16,
            top: 0,
            bottom: 0,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FloatingActionButton(
                    heroTag: "location",
                    mini: true,
                    backgroundColor: AppColors.neutral100,
                    foregroundColor: AppColors.neutral600,
                    elevation: 4,
                    onPressed: _getCurrentLocation,
                    child: _isLoading
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.primary),
                            ),
                          )
                        : const Icon(Icons.my_location, size: 20),
                  ),
                  const SizedBox(height: 8),
                  FloatingActionButton(
                    heroTag: "zoom_in",
                    mini: true,
                    backgroundColor: AppColors.neutral100,
                    foregroundColor: AppColors.neutral600,
                    elevation: 4,
                    onPressed: () {
                      _mapController?.animateCamera(CameraUpdate.zoomIn());
                    },
                    child: const Icon(Icons.add, size: 20),
                  ),
                  const SizedBox(height: 8),
                  FloatingActionButton(
                    heroTag: "zoom_out",
                    mini: true,
                    backgroundColor: AppColors.neutral100,
                    foregroundColor: AppColors.neutral600,
                    elevation: 4,
                    onPressed: () {
                      _mapController?.animateCamera(CameraUpdate.zoomOut());
                    },
                    child: const Icon(Icons.remove, size: 20),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),

          // Modern Bottom Sheet
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.neutral800.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.neutral200,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Location icon and title
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          Icons.location_on,
                          color: AppColors.primary,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Delivery Location',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: AppColors.neutral600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Address
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.neutral100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.place,
                          color: AppColors.neutral400,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _currentAddress,
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.neutral600,
                              height: 1.4,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Action button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _selectLocation,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: _isAddressReady
                          ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.white),
                              ),
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.check_circle, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'Confirm Location',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
